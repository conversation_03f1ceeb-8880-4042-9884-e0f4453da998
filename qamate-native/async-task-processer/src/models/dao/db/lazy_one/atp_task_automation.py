# -*- encoding: utf-8 -*-
"""
@Desc    :   Dao模块 - atp_task_automation.py

@File    :   atp_task_automation.py
@Time    :   2024-08-08
<AUTHOR>   <EMAIL>
"""
from src.models.dao.dao_handler import DaoHandler


class DaoDBAtpTaskAutomation(DaoHandler):
    """
    Dao AtpTaskAutomation 表操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.table_name = "atp_task_automation"

    def get_task_by_status(self, status):
        """
        根据 Status 返回 N 个符合条件的atp任务

        :param status: 筛选状态值, 详见 status 字段 DDL

        :return: 查询结果集。
        """
        result = self.db_lazyone.fetchall(
            f"""
            SELECT id, async_id, group_id, round_id, is_overwrite, extra
            FROM {self.table_name}
            WHERE status = %s
            """,
            status
        )
        return result

    def update_task_success_by_id(self, status, end_time, task_id):
        """
        修改atp任务状态, 成功

        :param status: 筛选状态值, 详见 status 字段 DDL
        :param end_time: 结束时间
        :param task_id: 主键 ID

        :return: None
        """
        effect_rows = self.db_lazyone.update(
            f"""
            UPDATE {self.table_name}
            SET status = %s, end_time = %s 
            WHERE id = %s
            """,
            (status, end_time, task_id)
        )
        if 0 == effect_rows:
            raise Exception("更新atp任务状态失败")

    def update_task_fail_by_id(self, status, extra_str, end_time, task_id):
        """
        修改atp任务状态, 失败

        :param status: 筛选状态值, 详见 status 字段 DDL
        :param extra_str: 额外信息(错误原因)
        :param end_time: 结束时间
        :param task_id: 主键 ID

        :return: None
        """
        effect_rows = self.db_lazyone.update(
            f"""
            UPDATE {self.table_name}
            SET status = %s, extra = %s, end_time = %s
            WHERE id = %s
            """,
            (status, extra_str, end_time, task_id)
        )
        if 0 == effect_rows:
            raise Exception("更新atp任务状态失败")

    def update_extra_by_id(self, extra_str, task_id):
        """
        修改atp任务extra信息

        :param extra_str: 额外信息(错误原因)
        :param task_id: 主键 ID

        :return: None
        """
        effect_rows = self.db_lazyone.update(
            f"""
            UPDATE {self.table_name}
            SET extra = %s
            WHERE id = %s
            """,
            (extra_str, task_id)
        )
        if 0 == effect_rows:
            raise Exception("更新atp任务extra失败")
