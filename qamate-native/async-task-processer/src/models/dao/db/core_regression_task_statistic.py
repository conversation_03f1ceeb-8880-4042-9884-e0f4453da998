# -*- encoding: utf-8 -*-
"""
@Desc    :   Dao模块 - core_regression_task_statistic

@File    :   core_regression_task_statistic.py
@Time    :   2024-11-04
<AUTHOR>   <EMAIL>
"""
from src.models.dao.dao_handler import DaoHandler


class DaoDBRegressionTaskStatistic(DaoHandler):
    """
    Dao DaoDBRegressionTaskStatistic 表操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.table_name = "core_regression_task_statistic"

    def get_record_by_task_id_and_os_type(self, task_id, os_type):
        """
        根据任务编号和端信息 获取统计记录

        :param task_id: 任务 ID
        :param os_type: 端信息
        :return: 查询结果集。
        """
        result = self.db.fetchone(
            f"""
            SELECT id
            FROM {self.table_name}
            WHERE task_id = %s AND os_type = %s
            """,
            (task_id, os_type)
        )
        return result

    def insert_record(self, record):
        """
        新增一行 统计记录

        :param record: 格式化统计记录
        :return: 新增 ID。
        """
        result = self.db.insert(
            f"""
            INSERT INTO {self.table_name}
            (task_id, plan_id, last_round_id, plan_name, os_type, plan_create_time, 
             statistic_update_time, module_id, module_name, total_num, auto_num, half_num, manual_num, 
             manual_unsign_num, manual_sign_num, half_unsign_num, half_sign_num, auto_unsign_num, auto_sign_num,
             manual_sign_pass, manual_sign_fail, half_sign_pass, half_sign_fail, auto_sign_pass, auto_sign_fail,
             execute_num, execute_success_num, execute_failed_num, execute_extra_num,
             manual_link_num, auto_link_num)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            (record["base"]["task_id"], record["base"]["plan_id"], record["base"]["last_round_id"],
             record["base"]["plan_name"], record["base"]["os_type"], record["base"]["plan_create_time"],
             record["base"]["statistic_update_time"], record["base"]["module_id"], record["base"]["module_name"],
             record["base"]["total_num"], record["base"]["auto_num"],
             record["base"]["half_num"], record["base"]["manual_num"],
             record["sign"]["manual_unsign_num"], record["sign"]["manual_sign_num"],
             record["sign"]["half_unsign_num"], record["sign"]["half_sign_num"],
             record["sign"]["auto_unsign_num"], record["sign"]["auto_sign_num"],
             record["sign"]["manual_sign_pass"], record["sign"]["manual_sign_fail"],
             record["sign"]["half_sign_pass"], record["sign"]["half_sign_fail"],
             record["sign"]["auto_sign_pass"], record["sign"]["auto_sign_fail"],
             record["execute"]["execute_num"], record["execute"]["execute_success_num"],
             record["execute"]["execute_failed_num"], record["execute"]["execute_extra_num"],
             record["link"]["manual_link_num"], record["link"]["auto_link_num"])
        )
        return result

    def update_record(self, record, record_id):
        """
        更新一条 统计记录

        :param record: 格式化统计记录
        """
        result = self.db.update(
            f"""
            UPDATE {self.table_name} 
            SET last_round_id = %s, statistic_update_time = %s, total_num = %s, auto_num = %s, 
            half_num = %s, manual_num = %s, manual_unsign_num = %s, manual_sign_num = %s, 
            half_unsign_num = %s, half_sign_num = %s, auto_unsign_num = %s, auto_sign_num = %s,
            manual_sign_pass = %s, manual_sign_fail = %s, 
            half_sign_pass = %s, half_sign_fail = %s, 
            auto_sign_pass = %s, auto_sign_fail = %s,
            execute_num = %s, execute_success_num = %s, execute_failed_num = %s, 
            execute_extra_num = %s, manual_link_num = %s, auto_link_num = %s
            WHERE id = %s
            """,
            (record["base"]["last_round_id"], record["base"]["statistic_update_time"],
             record["base"]["total_num"], record["base"]["auto_num"],
             record["base"]["half_num"], record["base"]["manual_num"],
             record["sign"]["manual_unsign_num"], record["sign"]["manual_sign_num"],
             record["sign"]["half_unsign_num"], record["sign"]["half_sign_num"],
             record["sign"]["auto_unsign_num"], record["sign"]["auto_sign_num"],
             record["sign"]["manual_sign_pass"], record["sign"]["manual_sign_fail"],
             record["sign"]["half_sign_pass"], record["sign"]["half_sign_fail"],
             record["sign"]["auto_sign_pass"], record["sign"]["auto_sign_fail"],
             record["execute"]["execute_num"], record["execute"]["execute_success_num"],
             record["execute"]["execute_failed_num"], record["execute"]["execute_extra_num"],
             record["link"]["manual_link_num"], record["link"]["auto_link_num"], record_id))
        return result

    def delete_task_by_plan_id_in(self, plan_list):
        """
        获取任务

        :param plan_ids: 计划 ID 列表
        :return: 查询结果集。
        """
        result = self.db.delete(
            f"""
            DELETE FROM {self.table_name}
            WHERE plan_id IN ({','.join(['%s'] * len(plan_list))})
            """,
            tuple(plan_list)
        )
        return result
