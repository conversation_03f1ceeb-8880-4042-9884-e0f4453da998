# -*- encoding: utf-8 -*-
"""
@Desc    :   Dao模块 - core_regression_round

@File    :   core_regression_round.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
from src.models.dao.dao_handler import <PERSON>oH<PERSON><PERSON>
from src.util.time import current_timestamp


class DaoDBRegressionRound(DaoHandler):
    """
    Dao DaoDBRegressionRound 表操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.table_name = "core_regression_round"

    def get_end_round(self, id_list):
        """
        获取执行完的轮次

        :return: 查询结果集。
        """
        if id_list is None or 0 == len(id_list):
            return []
        result = self.db.fetchall(
            f"""
            SELECT id
            FROM {self.table_name}
            WHERE id IN ({','.join(['%s'] * len(id_list))}) AND status = 1
            """,
            tuple(id_list)
        )
        return result

    def get_round_by_id(self, round_id):
        """
        获取轮次

        :return: 查询结果集。
        """
        result = self.db.fetchone(
            f"""
            SELECT task_id, plan_id
            FROM {self.table_name}
            WHERE id = %s
            """,
            (round_id)
        )
        return result

    def get_round_by_task_id(self, task_id):
        """
        根据任务ID获取轮次

        :param task_id: 任务ID
        :return: 查询结果集。
        """
        result = self.db.fetchall(
            f"""
            SELECT id, status, sign_task_id, end_time
            FROM {self.table_name}
            WHERE task_id = %s
            ORDER BY id DESC
            """,
            (task_id)
        )
        return result

    def get_round_by_plan_id_time(self, plan_id, start_time, end_time):
        """
        根据任务ID获取轮次

        :param task_id: 任务ID
        :return: 查询结果集。
        """
        result = self.db.fetchall(
            f"""
            SELECT id, status, sign_task_id, end_time
            FROM {self.table_name}
            WHERE status = 1 AND plan_id = %s AND create_time >= %s AND create_time <= %s
            ORDER BY id DESC
            """,
            (plan_id, start_time, end_time)
        )
        return result

    def get_round_by_plan_id_list(self, plan_id_list):
        """
        根据任务ID获取轮次

        :param task_id: 任务ID
        :return: 查询结果集。
        """
        if plan_id_list is None or 0 == len(plan_id_list):
            return []
        result = self.db.fetchall(
            f"""
            SELECT id, status, sign_task_id, end_time
            FROM {self.table_name}
            WHERE plan_id IN ({','.join(['%s'] * len(plan_id_list))})
            ORDER BY id DESC
            """,
            tuple(plan_id_list)
        )
        return result

    def get_round_by_task_list(self, id_list):
        """
        获取轮次

        :return: 查询结果集。
        """
        if id_list is None or 0 == len(id_list):
            return []
        result = self.db.fetchall(
            f"""
            SELECT id, task_id, create_time, status
            FROM {self.table_name}
            WHERE task_id IN ({','.join(['%s'] * len(id_list))})
            ORDER BY id
            """,
            tuple(id_list)
        )
        return result

    def get_round_by_module_and_time(self, module_id, start_time, end_time):
        """
        根据 module_id 及 时间区间 来获取轮次列表

        :param module_id: 模块ID
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: 查询结果集。
        """
        result = self.db.fetchall(
            f"""
            SELECT crr.id, crr.status, crr.sign_task_id, crr.end_time
            FROM core_regression_round crr
                JOIN core_regression_plan crp ON crp.id = crr.plan_id
                JOIN core_sign_task sct ON crr.sign_task_id = sct.id
            WHERE crp.module_id = %s AND sct.status <> 3 AND crr.create_time >= %s AND crr.create_time <= %s
            ORDER BY crr.id DESC
            """,
            (module_id, start_time, end_time)
        )
        return result
    
    def get_sign_task_by_module_and_time_lower_id(self, module_id, start_time, id):
        """
        根据 module_id 及 时间 及 小于id 来获取轮次列表

        :param module_id: 模块ID
        :param start_time: 开始时间
        :param id: 小于 id
        :return: 查询结果集。
        """
        result = self.db.fetchall(
            f"""
            SELECT crr.id, crr.sign_task_id
            FROM core_regression_round crr
                JOIN core_regression_plan crp ON crp.id = crr.plan_id
                JOIN core_sign_task sct ON crr.sign_task_id = sct.id
            WHERE crp.module_id = %s AND sct.status <> 3 AND crr.create_time < %s AND crr.id < %s
            ORDER BY crr.id DESC
            LIMIT 1500
            """,
            (module_id, start_time, id)
        )
        return result
