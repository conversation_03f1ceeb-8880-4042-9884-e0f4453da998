# -*- encoding: utf-8 -*-
"""
@Desc    :   Dao模块 - core_regression_task

@File    :   core_regression_task.py
@Time    :   2024-10-23
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
from src.models.dao.dao_handler import <PERSON>oHand<PERSON>
from src.util.time import current_timestamp


class DaoDBRegressionTask(DaoHandler):
    """
    Dao DaoDBRegressionTask 表操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.table_name = "core_regression_task"

    def get_task_by_plan_id(self, plan_id):
        """
        获取任务

        :param plan_id: 计划 ID
        :return: 查询结果集。
        """
        result = self.db.fetchall(
            f"""
            SELECT id, os_type, tree_node_id
            FROM {self.table_name}
            WHERE plan_id = %s AND is_del = 0
            """,
            (plan_id)
        )
        return result
    
    def get_task_by_plan_id_in(self, plan_id):
        """
        获取任务

        :param plan_ids: 计划 ID 列表
        :return: 查询结果集。
        """
        result = self.db.fetchall(
            f"""
            SELECT id, os_type, tree_node_id
            FROM {self.table_name}
            WHERE plan_id IN ({','.join(['%s'] * len(plan_id))}) AND is_del = 0
            """,
            tuple(plan_id)
        )
        return result

    def get_task_by_id(self, task_id):
        """
        获取任务

        :param plan_id: 计划 ID
        :return: 查询结果集。
        """
        result = self.db.fetchone(
            f"""
            SELECT id, tree_node_id
            FROM {self.table_name}
            WHERE id = %s AND is_del = 0
            """,
            (task_id)
        )
        return result
