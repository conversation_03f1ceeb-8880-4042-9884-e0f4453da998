# -*- encoding: utf-8 -*-
"""
@Desc    :   Server lazymind 服务端操作类

@File    :   lazymind.py
@Time    :   2024-08-12
<AUTHOR>   <EMAIL>
"""
import json
import requests
from src.models.dao.dao_handler import DaoHandler


class DaoServerLazymind(DaoHandler):
    """
    Server lazymind 服务端操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()

    def get_mind_node(self, round_id):
        """
        获取用例执行结果, 返回树状结构的node
        """
        res = requests.post(
            "{}/lazymind/sign/task/query/detail".format(self.base_url),
            data=json.dumps({
                "taskId": round_id,
            }),
            headers={
                "Content-Type": "application/json",
            },
            timeout=5
        )

        node_result = json.loads(res.text)
        if node_result["code"] != 0:
            raise Exception(
                "API-获取脑图node结果失败: {}".format(node_result["msg"])
            )
        else:
            return node_result["data"]

    def update_sign_res(self, round_id, node_auto_map):
        """
        更新脑图签章结果
        """
        res = requests.post(
            "{}/lazymind/sign/task/createSignRecordBatch".format(self.base_url),
            data=json.dumps({
                "mindRoundId": round_id,
                "nodeAutoMap": node_auto_map,
            }),
            headers={
                "Content-Type": "application/json",
            },
            timeout=3
        )

        node_result = json.loads(res.text)
        if node_result["code"] != 0:
            raise Exception(
                "API-更新签章结果失败: {}".format(node_result["msg"])
            )
        else:
            return node_result
