# -*- encoding: utf-8 -*-
"""
@Desc    :   Server base 服务端操作类

@File    :   base.py
@Time    :   2024-08-12
<AUTHOR>   <EMAIL>
"""
import json
import requests
from src.models.dao.dao_handler import DaoHandler


class DaoServerBase(DaoHandler):
    """
    Server base 服务端操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()

    def update_async_status(self, id, status, status_info):
        """
        base, 更新异步任务状态
        """
        res = requests.post(
            "{}/base/asyncTask/updateStatus".format(self.base_url),
            data=json.dumps({
                "id": id,
                "status": status,
                "statusInfo": status_info,
            }),
            headers={
                "Content-Type": "application/json",
            },
            timeout=5
        )

        update_result = json.loads(res.text)
        if update_result["code"] != 0:
            raise Exception(
                "API-更新异步任务状态失败: {}".format(update_result["msg"])
            )
        else:
            return update_result
