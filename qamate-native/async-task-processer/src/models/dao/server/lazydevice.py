# -*- encoding: utf-8 -*-
"""
@Desc    :   Server LazyCloud 服务端操作类

@File    :   lazycloud.py
@Time    :   2024-07-30
<AUTHOR>   wang<PERSON><EMAIL>
"""
import json
import requests
from src.models.dao.dao_handler import DaoHandler


class DaoServerLazydevice(DaoHandler):
    """
    Server LazyDevice 服务端操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()

    def get_whole_pool_list(self):
        """
        获取所有设备池列表

        :return: 整个设备池 List
        """
        result = requests.post(
            "{}/core/cloud/pool/list".format(self.base_url),
            headers={
                "Content-Type": "application/json"
            },
            timeout=5
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取设备池列表失败")
        return result["data"]

    def occupy_pool(self, pool_id, plan_id):
        """
        占用设备池

        :param pool_id: 设备池 ID
        :param plan_id: 执行计划 ID

        :return: 整个设备池 List
        """
        result = requests.post(
            "{}/core/cloud/pool/occupy".format(self.base_url),
            data=json.dumps({
                "planId": plan_id,
                "poolId": pool_id
            }),
            headers={
                "Content-Type": "application/json"
            },
            timeout=5
        )
        result = json.loads(result.text)
        return not ("code" not in result or 0 != result["code"])

    def release_pool(self, pool_id, plan_id):
        """
        释放设备池

        :param pool_id: 设备池 ID
        :param plan_id: 执行计划 ID

        :return: 整个设备池 List
        """
        result = requests.post(
            "{}/core/cloud/pool/release".format(self.base_url),
            data=json.dumps({
                "planId": plan_id,
                "poolId": pool_id
            }),
            headers={
                "Content-Type": "application/json"
            },
            timeout=5
        )
        result = json.loads(result.text)
        return not ("code" not in result or 0 != result["code"])

    def get_pool_device_list(self, pool_id):
        """
        获取设备池中设备情况

        :param pool_id: 设备池 ID

        :return: 整个设备池 List
        """
        result = requests.post(
            "{}/lazydevice/cloud/filter".format(self.base_url),
            data=json.dumps({
                "poolId": pool_id
            }),
            headers={
                "Content-Type": "application/json"
            },
            timeout=5
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取设备池列表失败")
        return result["data"]["deviceList"]

    def get_device_list_by_id(self, id_list):
        """
        获取设备列表中设备情况

        :param id_list: 设备 ID

        :return: 整个设备池 List
        """
        if 0 == len(id_list):
            return []
        result = requests.post(
            "{}/lazydevice/cloud/filter".format(self.base_url),
            data=json.dumps({
                "deviceIds": id_list
            }),
            headers={
                "Content-Type": "application/json"
            },
            timeout=5
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取设备池列表失败")
        return result["data"]["deviceList"]
