# -*- encoding: utf-8 -*-
"""
@Desc    :   Server Core 服务端操作类

@File    :   core.py
@Time    :   2024-07-30
<AUTHOR>   wang<PERSON><PERSON><EMAIL>
"""
import json

import requests
from src.models.dao.dao_handler import DaoHandler


class DaoServerCore(DaoHandler):
    """
    Server Core 服务端操作类
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.headers = {
            "Content-Type": "application/json",
            "Accept-Encoding": "gzip",
            "QAMate-ModuleId": self.openapi_token["module_id"],
            "QAMate-Token": self.openapi_token["token"]
        }

    def get_env_by_id(self, env_id):
        """
        获取指定环境的详情信息

        :param env_id: 环境 ID

        :return: 整个环境信息
        """
        result = requests.post(
            "{}/core/module/env/detail".format(self.base_url),
            data=json.dumps({
                "envId": env_id,
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取环境失败")
        return result["data"]["envDetail"]

    def get_env_list(self, module_id, os_type):
        """
        获取环境列表

        :param module_id: 模块 ID
        :param os_type: 系统类型

        :return: 整个环境列表
        """
        result = requests.post(
            "{}/core/module/env/list".format(self.base_url),
            data=json.dumps({
                "moduleId": module_id,
                "osType": os_type,
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取环境失败")
        return result["data"]["envList"]

    def get_case_by_case_root(self, case_root_id):
        """
        获取指定根节点的测试用例

        :param case_root_id: 根节点 ID

        :return: 整个用例树 List
        """
        result = requests.post(
            "{}/core/case/tree/query".format(self.base_url),
            data=json.dumps({
                "caseRootId": case_root_id,
                "withAuto": True
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取完整用例树失败")
        if result["data"] is None:
            return []
        return [result["data"]]

    def get_template_by_module(self, module_id, os_type):
        """
        获取指定模块的测试模板用例

        :param module_id: 模块 ID
        :param os_type: 端类型

        :return: 模板用例列表
        """
        result = requests.post(
            "{}/core/module/snippet/list".format(self.base_url),
            data=json.dumps({
                "moduleId": module_id,
                "osType": os_type
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取完整模板用例树失败")
        return result["data"]

    def get_app_list_by_module(self, module_id, os_type):
        """
        获取指定模块的 APP 列表

        :param module_id: 模块 ID
        :param os_type: 端类型

        :return: APP 列表
        """
        result = requests.post(
            "{}/core/module/app/list".format(self.base_url),
            data=json.dumps({
                "moduleId": module_id,
                "osType": os_type
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取 APP 列表失败")
        return result["data"]["appList"]

    def get_step_by_case_node(self, case_node_id, os_type):
        """
        获取指定节点的步骤列表

        :param case_node_id: 用例 ID
        :param os_type: 端类型

        :return: 步骤列表 List
        """
        result = requests.post(
            "{}/core/case/node/step/list".format(self.base_url),
            data=json.dumps({
                "caseNodeId": case_node_id,
                "osType": os_type
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取步骤列表失败")
        return result["data"]["stepList"]

    def get_3_0_white_list(self):
        """
        获取 3.0 白名单

        :return: 白名单业务 List
        """
        result = requests.post(
            "{}/core/module/v3/whitelist".format(self.base_url),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取白名单列表失败")
        return result["data"]["moduleIdList"]

    def get_scheme_by_module_id(self, module_id, os_type):
        """
        根据 module_id 获取scheme模板

        :param module_id: 模块 ID
        :param os_type: 端类型

        :return: List 模板列表
        """
        result = requests.post(
            "{}/core/module/scheme/list".format(self.base_url),
            data=json.dumps({
                "moduleId": module_id,
                "osType": os_type
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取scheme列表失败")
        return result["data"]

    def callback_3_0_plan_status(self, plan_id, status, msg=""):
        """
        回调 3.0 计划状态

        :param plan_id: 计划 ID
        :param status: 计划状态
        :param msg: 回调信息

        :return: None
        """
        result = requests.post(
            "{}/core/regression/automan/planCallback".format(self.base_url),
            data=json.dumps({
                "planId": plan_id,
                "status": status,
                "msg": msg
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("回调计划状态失败")

    def callback_3_0_task_status(self, plan_id, case_node_id, status, result="", msg=""):
        """
        回调 3.0 任务状态

        :param plan_id: 计划 ID
        :param case_node_id: 用例 ID
        :param status: 计划状态
        :param result: 任务结果
        :param msg: 回调信息

        :return: None
        """
        result = requests.post(
            "{}/core/regression/automan/taskCallback".format(self.base_url),
            data=json.dumps({
                "planId": plan_id,
                "caseNodeId": case_node_id,
                "result": result,
                "status": status,
                "msg": msg
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("回调计划状态失败")

    def sign_case(self, case_node_id_list, os_type, status, plan_id, create_user):
        """
        完成用例签章

        :param case_node_id_list: 用例 ID
        :param os_type: 端类型
        :param status: 签章状态
        :param plan_id: 自动化计划ID结果

        :return: None
        """
        data = {}
        if 1 == len(case_node_id_list) and -1 != plan_id:
            data = {
                "caseNodeIdList": case_node_id_list,
                "osType": os_type,
                "signType": 2,
                "status": status,
                "exectionType": 2,
                "extra": {
                    "cloudPlanId": plan_id,
                    "caseNodeId": case_node_id_list[0]
                },
                "createUser": create_user,
            }
        else:
            data = {
                "caseNodeIdList": case_node_id_list,
                "osType": os_type,
                "signType": 2,
                "status": status,
                "exectionType": 2,
                "createUser": create_user,
            }
        result = requests.post(
            "{}/core/sign/node/create".format(self.base_url),
            data=json.dumps(data),
            headers=self.headers,
            timeout=30
        )
        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("签章失败")

    def cancel_regression_automan(self, round_id, type):
        """
        取消轮次自动化

        :param round_id: 轮次ID
        :param type: 端类型
        :return: None
        """
        result = requests.post(
            "{}/core/regression/automan/cancel".format(self.base_url),
            data=json.dumps({
                "roundId": round_id,
                "type": type
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("取消失败")
    
    def cancel_cloud_plan(self, plan_id):
        """
        取消自动化

        :param plan_id: 计划ID
        :return: None
        """
        result = requests.post(
            "{}/core/cloud/plan/cancel".format(self.base_url),
            data=json.dumps({
                "planId": plan_id
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("取消失败")

    def create_regression_plan(self, params):
        """
        创建测试计划

        :param params: 创建参数
        :return: 测试计划 ID
        """
        result = requests.post(
            "{}/core/regression/plan/create".format(self.base_url),
            data=params,
            headers=self.headers,
            timeout=300
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("创建失败")
        return result["data"]["planId"]

    def create_automan_plan(self, params):
        """
        创建自动化小助手计划

        :param params: 创建参数
        :return: Cloud Plan ID
        """
        result = requests.post(
            "{}/core/regression/automan/create".format(self.base_url),
            data=params,
            headers=self.headers,
            timeout=300
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("创建失败")
        return result["data"]["cloudPlanId"]

    def get_plan_info_by_id(self, plan_id):
        """
        查询测试计划详情

        :param plan_id: 测试计划 ID
        :return: 测试计划详情
        """
        result = requests.post(
            "{}/core/regression/plan/detail".format(self.base_url),
            data=json.dumps({
                "planIdList": [plan_id]
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("查询失败")
        return result["data"]["planList"][0]

    def get_round_detail_by_round_id(self, round_id):
        """
        查询轮次详情

        :param round_id: 轮次 ID
        :return: 轮次详情
        """
        result = requests.post(
            "{}/core/regression/round/detail".format(self.base_url),
            data=json.dumps({
                "roundId": round_id
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("查询失败")
        return result["data"]

    def get_round_list_by_task_id(self, task_id):
        """
        查询轮次列表

        :param task_id: 任务 ID
        :return: 轮次列表
        """
        result = requests.post(
            "{}/core/regression/round/list".format(self.base_url),
            data=json.dumps({
                "taskId": task_id
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("查询失败")
        return result["data"]["roundList"]

    def end_round(self, round_id):
        """
        结束轮次

        :param round_id: 轮次ID
        :return: None
        """
        result = requests.post(
            "{}/core/regression/round/end".format(self.base_url),
            data=json.dumps({
                "roundId": round_id
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("结束失败")

    def open_round(self, task_id):
        """
        开启新轮次

        :param task_id: 任务ID
        :return: 新轮次 ID
        """
        result = requests.post(
            "{}/core/regression/round/new".format(self.base_url),
            data=json.dumps({
                "taskId": task_id,
                "forkType": 1,
                "onlyAutoCase": True
            }),
            headers=self.headers,
            timeout=300
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("开启失败")
        return result["data"]["roundId"]

    def disable_case(self, case_id_list, os_type, message):
        """
        禁用用例

        :param case_id_list: 用例ID

        :return: None
        """
        result = requests.post(
            "{}/core/case/node/update".format(self.base_url),
            data=json.dumps({
                "caseNodeIdList": case_id_list,
                "osDetail": {
                    "osType": os_type,
                    "disabledInfo": {
                        "status": True,
                        "type": 2,
                        "message": message
                    }
                }
            }),
            headers=self.headers,
            timeout=300
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("禁用失败")

    def get_digital_node_info(self, digital_node_id):
        """
        获取数据节点详细信息

        :param digital_node_id: 数据节点ID

        :return: Dict 数据节点信息
        """
        result = requests.post(
            "{}/core/proxy/digital/node/detail".format(self.base_url),
            data=json.dumps({
                "digitalNodeId": digital_node_id
            }),
            headers=self.headers,
            timeout=300
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("获取失败")
        if isinstance(result["data"]["digitalInfo"], str):
            return json.loads(result["data"]["digitalInfo"])
        return result["data"]["digitalInfo"]

    def create_template_task(self, template_id):
        """
        创建模版任务

        :param template_id: 模版ID
        :return: 任务ID
        """
        result = requests.post(
            "{}/core/plan/server/template/task/new".format(self.base_url),
            data=json.dumps({
                "planTemplateId": template_id
            }),
            headers=self.headers,
            timeout=30
        )

        result = json.loads(result.text)
        if "code" not in result or 0 != result["code"]:
            raise Exception("创建模版任务失败")
        return result["data"]["taskId"]
