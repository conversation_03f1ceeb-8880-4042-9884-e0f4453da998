# -*- encoding: utf-8 -*-
"""
@Desc    :   试用于 QAMate 3.0 逻辑的计划拆分脚本

@File    :   qamate_3_0.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import uuid
import copy
import json
from src.models.service.data.cloud.cloud_case import ServiceDataCloudCase
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.dao.server.core import DaoServerCore
from src.util.time import current_timestamp
from src.util.bos import BosService
from src.lib.logger import logger

PLAN_TYPE_OS_MAPPING = {
    1: 1,
    6: 1,
    8: 1,
    2: 2,
    7: 2,
    9: 2
}


class ServiceDataCloudPlanSplitQAMate3:
    """
    脚本执行类
    """

    def __init__(self, plan_item):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.data_cloud_case = ServiceDataCloudCase()
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_server_core = DaoServerCore()
        self.bos = BosService()

        self.plan_item = plan_item
        self.plan_params = None
        self.app_list = None
        self.install_app_list = []
        self.login_info = {
            "username": "",
            "password": ""
        }
        self.os_type = PLAN_TYPE_OS_MAPPING.get(plan_item["plan_type"])
        self.case_tree = self.data_cloud_case.get_case_tree_by_case_root(
            case_root_id=plan_item["case_root_id"], os_type=self.os_type)
        self.case_snippet = self.data_cloud_case.get_template_by_module_id(
            module_id=plan_item["module_id"], os_type=self.os_type)
        self.scheme_list = self.dao_server_core.get_scheme_by_module_id(
            module_id=plan_item["module_id"], os_type=self.os_type)
        self.setup_snippet = self.data_cloud_case.get_login_setup_snippets(os_type=self.os_type)
        self.setup_scheme_list = self.data_cloud_case.get_login_setup_scheme(os_type=self.os_type)
        self.task_ext = {}

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.data.cloud.plan_split.qamate_3_0.{}]".format(method_name)

    def get_app_list(self):
        """
        获取APP模板库
        """
        app_list = []
        if (
                "envParams" in self.plan_params
                and "envDetail" in self.plan_params["envParams"]
                and "appList" in self.plan_params["envParams"]["envDetail"]
        ):
            app_list = self.plan_params["envParams"]["envDetail"]["appList"]
        return app_list

    def handle_task_ext(self):
        # 处理 sysAlertClear 参数
        self.task_ext["sysAlertClear"] = True
        if "planParams" not in self.plan_params:
            return
        if "sysAlertClear" in self.plan_params["planParams"]:
            self.task_ext["sysAlertClear"] = self.plan_params["planParams"]["sysAlertClear"]
        # 处理 server 特有参数 modeConfig 和 modeType
        try:
            if (
                    "modeConfig" in self.plan_params["planParams"]
                    and "modeType" in self.plan_params["planParams"]
                    and self.plan_params["planParams"]["modeConfig"] is not None
            ):
                self.task_ext["modeType"] = self.plan_params["planParams"]["modeType"]
                self.task_ext["modeConfig"] = self.plan_params["planParams"]["modeConfig"]
                self.task_ext["modeConfig"]["serverConfig"]["stableEnvironment"] = self.dao_server_core.get_env_by_id(
                    env_id=self.task_ext["modeConfig"]["serverConfig"]["stableEnvironmentId"])
                self.task_ext["modeConfig"]["serverConfig"]["testEnvironment"] = self.dao_server_core.get_env_by_id(
                    env_id=self.task_ext["modeConfig"]["serverConfig"]["testEnvironmentId"])
        except:
            pass

    def upload_case_file(self, case_list):
        """
        处理上传用例到 BOS 的逻辑

        :param case_list (Array): 用例列表

        :return: 用例 BOS 链接
        """
        case_content = {
            "ext": self.task_ext,
            "envDetail": {},
            "paramsList": [],
            "caseList": case_list
        }
        # 获取计划参数列表
        if (
                "envParams" in self.plan_params
                and "envDetail" in self.plan_params["envParams"]
        ):
            case_content["envDetail"] = self.plan_params["envParams"]["envDetail"]
            if "paramList" in self.plan_params["envParams"]["envDetail"]:
                for params in self.plan_params["envParams"]["envDetail"]["paramList"]:
                    case_content["paramsList"].append({
                        "name": params["paramKey"],
                        "value": params["envValue"]
                    })
        # 上传 BOS 文件
        return self.bos.upload_from_json(
            json_object=case_content,
            bos_path='/lazycloud/case/case/{}_{}_{}.json'.format(
                self.plan_item["plan_id"], current_timestamp(), uuid.uuid4())
        )

    def handle_one_step(self, step, case_snippet=[], snippet_id=None):
        """
        处理一个步骤, 解析测试片段、弹窗点除、APP包名

        :param step (Object): 步骤信息
        :param app_list (Array): App 列表

        :return (Object): 步骤信息
        """
        if len(case_snippet) == 0:
            case_snippet = self.case_snippet
        # 步骤类型和 APP 包名相关
        if (
                "type" in step["stepInfo"]
                and 1 == step["stepInfo"]["type"]
                and step["stepInfo"]["params"]["type"] in ["closeApp", "launchApp", "clearApp", "authApp", "installApp",
                                                           "login"]
                and "id" in step["stepInfo"]["params"]["params"]
        ):
            for app in self.app_list:
                if app["appId"] == step["stepInfo"]["params"]["params"]["id"]:
                    step["stepInfo"]["params"]["params"]["packageName"] = app["envValue"]
                    break
        # 步骤类型是 412 测试片段
        if 412 == step["stepType"]:
            for snippet in case_snippet:
                # 防止循环嵌套自身模板
                if snippet["id"] == snippet_id:
                    continue
                if "id" in step["stepInfo"]["params"]["params"] and snippet["id"] == \
                        step["stepInfo"]["params"]["params"]["id"]:
                    step["stepInfo"]["params"]["params"]["step"] = []
                    for snippet_step in snippet["step"]:
                        step["stepInfo"]["params"]["params"]["step"] += self.handle_one_step(
                            copy.deepcopy(snippet_step), snippet_id=snippet["id"])
        # 步骤类型是 1401 步骤组
        if 1401 == step["stepType"]:
            step_children = step["stepChildren"]
            step_group_list = []
            for step_id in step["stepInfo"]["params"]["params"]["stepIdList"]:
                for step_item in step_children:
                    if step_id == step_item["stepId"]:
                        step_group_list += self.handle_one_step(copy.deepcopy(step_item), case_snippet)
                        break
            step["stepInfo"]["params"]["params"]["step"] = step_group_list
        # 步骤类型是 411 弹窗点除
        elif 411 == step["stepType"]:
            step["stepInfo"]["params"]["params"]["popDetail"] = []
            for snippet in case_snippet:
                # 防止循环嵌套自身模板
                if snippet["id"] == snippet_id:
                    continue
                if snippet["id"] in step["stepInfo"]["params"]["params"]["popList"]:
                    pop_item = copy.deepcopy(snippet)
                    pop_item["step"] = []
                    for snippet_step in snippet["step"]:
                        pop_item["step"] += self.handle_one_step(copy.deepcopy(snippet_step), snippet_id=snippet["id"])
                    step["stepInfo"]["params"]["params"]["popDetail"].append(copy.deepcopy(pop_item))
        # 步骤类型是 402 Scheme
        elif 402 == step["stepType"] and "id" in step["stepInfo"]["params"]["params"]:
            for scheme in self.scheme_list + self.setup_scheme_list:
                if scheme["schemeId"] == step["stepInfo"]["params"]["params"]["id"]:
                    step["stepInfo"]["params"]["params"]["scheme"] = scheme["schemeContent"]
                    break
        # 步骤类型是 Mock 相关的
        elif step["stepType"] in [408, 409, 503] and "digitalNodeId" in step["stepInfo"]["params"]["params"]:
            digital_node_id = step["stepInfo"]["params"]["params"]["digitalNodeId"]
            step["stepInfo"]["params"]["params"] = self.dao_server_core.get_digital_node_info(
                digital_node_id=digital_node_id)
            step["stepInfo"]["params"]["params"]["digitalNodeId"] = digital_node_id
        # 步骤类型是 424 安装 App
        elif 424 == step["stepType"]:
            # 处理覆盖安装需要用测试计划的参数
            if (
                    "installType" in step["stepInfo"]["params"]["params"]
                    and "packageName" in step["stepInfo"]["params"]["params"]
                    and 2 == step["stepInfo"]["params"]["params"]["installType"]
            ):
                file_link = ""
                for app_item in self.install_app_list:
                    if app_item["package_name"] == step["stepInfo"]["params"]["params"]["packageName"]:
                        file_link = app_item["file_link"]
                        break
                step["stepInfo"]["params"]["params"]["fileLink"] = file_link
        # 步骤类型是 425 登录
        elif 425 == step["stepType"]:
            if "packageName" in step["stepInfo"]["params"]["params"]:
                step_list = []
                for setup_item in self.setup_snippet:
                    setup_app_name = ""
                    # 寻找开启 App 步骤, 并锁定这是哪个 App 的初始化步骤
                    for step in setup_item["step"]:
                        if 401 == step["stepType"] and "packageName" in step["stepInfo"]["params"]["params"]:
                            setup_app_name = step["stepInfo"]["params"]["params"]["packageName"]
                            break
                    if setup_app_name != step["stepInfo"]["params"]["params"]["packageName"]:
                        continue
                    for setup_step in setup_item["step"]:
                        setup_step = self.handle_one_step(copy.deepcopy(setup_step))[0]
                        if "" != self.login_info["username"]:
                            step_info = json.dumps(setup_step["stepInfo"], ensure_ascii=False)
                            step_info = step_info.replace("${ONE_USERNAME}", self.login_info["username"])
                            step_info = step_info.replace("${ONE_PASSWORD}", self.login_info["password"])
                            setup_step["stepInfo"] = json.loads(step_info)
                        step_list.append(copy.deepcopy(setup_step))
                    break
                return step_list
        return [step]

    def handle_one_case(self, case_list):
        """
        处理一个任务, 解析环境前置、App替换、弹窗点除等操作信息

        :param case_list (Array): 用例列表

        :return (Object): 用例 初始化需要 & BOS 链接
        """
        # 处理前置环境处理步骤
        before_node = {
            "caseNodeId": -1,
            "nodeType": 2,
            "nodeName": "before each",
            "step": [
                {
                    "stepId": -2,
                    "stepType": 408,
                    "stepDesc": "",
                    "stepInfo": {
                        "type": 1,
                        "params": {
                            "type": "clearProxy",
                            "params": {
                                "willVerify": False
                            }
                        }
                    }
                }
            ]
        }
        # 处理用户自定义的环境前置操作
        if not ("stageParams" not in self.plan_params or "beforeEach" not in self.plan_params["stageParams"]):
            for item in self.plan_params["stageParams"]["beforeEach"]:
                if 409 == item["stepType"]:
                    before_node["step"].append({
                        "stepId": -2,
                        "stepType": 409,
                        "stepDesc": "",
                        "stepInfo": {
                            "type": 1,
                            "params": {
                                "type": "requestRedirect",
                                "params": item["stepInfo"]["params"]
                            }
                        }
                    })
                if 402 == item["stepType"]:
                    before_node["step"].append({
                        "stepId": -2,
                        "stepType": 402,
                        "stepDesc": "",
                        "stepInfo": {
                            "type": 1,
                            "params": {
                                "type": "scheme",
                                "params": item["stepInfo"]["params"]
                            }
                        }
                    })
        # 处理原任务中的步骤
        before_snippet_set = set()
        after_snippet_set = set()
        setup_case_set = set()
        teardown_case_set = set()
        is_penging_manual = False
        for node in case_list:
            if "nodeType" in node and 4 == node["executionType"]:
                is_penging_manual = True
            if "beforeAllInfo" in node:
                before_snippet_set = before_snippet_set | set(node["beforeAllInfo"])
            if "afterAllInfo" in node:
                after_snippet_set = after_snippet_set | set(node["afterAllInfo"])
            if "setupInfo" in node and node["setupInfo"] is not None:
                setup_case_set.add(node["setupInfo"])
            if "teardownInfo" in node and node["teardownInfo"] is not None:
                teardown_case_set.add(node["teardownInfo"])
            node["new_step"] = []
            for step in node["step"]:
                node["new_step"] += self.handle_one_step(copy.deepcopy(step))
            node["step"] = copy.deepcopy(node["new_step"])
            del node["new_step"]
        case_list.insert(0, before_node)
        return {
            "before_snippet_set": before_snippet_set,
            "after_snippet_set": after_snippet_set,
            "setup_snippet_set": setup_case_set,
            "teardown_case_set": teardown_case_set,
            "case_file": self.upload_case_file(case_list),
            "is_penging_manual": is_penging_manual
        }

    def create_setup_task(self):
        """
        生成前置装包任务

        :param plan_item (Object): 计划详细信息

        :return: 装包任务列表
        """
        # 检测是否需要装包初始化任务
        if "stageParams" not in self.plan_params or "beforeAll" not in self.plan_params["stageParams"]:
            return []
        app_list = []
        for item in self.plan_params["stageParams"]["beforeAll"]:
            if 413 == item["stepType"]:
                self.install_app_list.append({
                    "package_name": item["stepInfo"]["params"]["packageName"],
                    "file_link": item["stepInfo"]["params"]["fileLink"]
                })
                app_list.append({
                    "package_name": item["stepInfo"]["params"]["packageName"],
                    "file_link": item["stepInfo"]["params"]["fileLink"],
                    "case_list": [
                        {
                            "caseNodeId": -1,
                            "nodeType": 2,
                            "nodeName": "APP安装节点",
                            "step": [
                                {
                                    "stepId": -2,
                                    "stepType": 424,
                                    "stepDesc": "",
                                    "stepInfo": {
                                        "type": 1,
                                        "params": {
                                            "type": "installApp",
                                            "params": {
                                                "packageName": item["stepInfo"]["params"]["packageName"],
                                                "fileLink": item["stepInfo"]["params"]["fileLink"],
                                                "installType": 1
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    "got_snippet": False
                })
        if 0 == len(app_list):
            return []
        logger.info(
            "{} ID {} 需要生成前置装包任务".format(self.get_log_tag("create_setup_task"), self.plan_item["plan_id"]))
        # 遍历初始化模板并组装需要的装包任务
        for setup_item in self.setup_snippet:
            setup_app_name = ""
            # 寻找开启 App 步骤, 并锁定这是哪个 App 的初始化步骤
            for step in setup_item["step"]:
                if 401 == step["stepType"] and "packageName" in step["stepInfo"]["params"]["params"]:
                    setup_app_name = step["stepInfo"]["params"]["params"]["packageName"]
                    break
            # 寻找是否有对应的装包需求
            if "" == setup_app_name:
                continue
            # 生成装包任务
            for app_item in app_list:
                if setup_app_name != app_item["package_name"]:
                    continue
                new_step = []
                for step in setup_item["step"]:
                    new_step += self.handle_one_step(copy.deepcopy(step), case_snippet=self.setup_snippet)
                app_item["case_list"].append({
                    "caseNodeId": -1 * setup_item["id"],
                    "nodeType": 2,
                    "nodeName": setup_item["templateName"],
                    "step": copy.deepcopy(new_step),
                })
                app_item["got_snippet"] = True
                break
        # 判断是否需要指定登陆账号并先行替换参数
        for item in self.plan_params["stageParams"]["beforeAll"]:
            if 414 != item["stepType"]:
                continue
            self.login_info["username"] = item["stepInfo"]["params"]["username"]
            self.login_info["password"] = item["stepInfo"]["params"]["password"]
            for app_item in app_list:
                for node in app_item["case_list"]:
                    node["newStep"] = []
                    for step in node["step"]:
                        new_step = copy.deepcopy(step)
                        step_info = json.dumps(new_step["stepInfo"], ensure_ascii=False)
                        step_info = step_info.replace("${ONE_USERNAME}", self.login_info["username"])
                        step_info = step_info.replace("${ONE_PASSWORD}", self.login_info["password"])
                        new_step["stepInfo"] = json.loads(step_info)
                        node["newStep"].append(new_step)
                    node["step"] = copy.deepcopy(node["newStep"])
                    del node["newStep"]
            break
        # 创建任务
        case_list = []
        for app_item in app_list:
            if True == app_item["got_snippet"] or 0 == len(case_list):
                case_list = app_item["case_list"]
        task_list = []
        if 0 != len(case_list):
            task_list.append(self.dao_core_cloud_task.insert_task(
                plan_id=self.plan_item["plan_id"],
                case_id=0,
                task_info=json.dumps({
                    "caseFile": self.upload_case_file(case_list)
                }, ensure_ascii=False),
                type=2,
                retry_times=0
            ))
            logger.info("{} ID {} 生成前置装包任务 taskId {}".format(self.get_log_tag("create_setup_task"),
                                                                     self.plan_item["plan_id"], task_list[0]))
        return task_list

    def create_dependency_task(self, snippet_list, task_type):
        """
        处理初始化任务的创建

        :param snippet_list (Array): 需要用到的初始化任务列表
        :param task_type (Integer): 任务类型

        :return : 任务ID
        """
        # 生成具体任务内容
        case_list = []
        for snippet_item in self.case_snippet:
            if snippet_item["id"] in snippet_list:
                new_step = []
                for step in snippet_item["step"]:
                    new_step += self.handle_one_step(copy.deepcopy(step))
                case_list.append({
                    "caseNodeId": -1 * snippet_item["id"],
                    "nodeType": 2,
                    "nodeName": snippet_item["templateName"],
                    "step": copy.deepcopy(new_step)
                })
        # 插入一条任务记录
        task_id = self.dao_core_cloud_task.insert_task(
            plan_id=self.plan_item["plan_id"],
            case_id=0,
            task_info=json.dumps({
                "caseFile": self.upload_case_file(case_list)
            }, ensure_ascii=False),
            type=task_type,
            retry_times=0
        )
        return task_id

    def create_new_dependency_task(self, case_node_list, task_type):
        """
        处理初始化任务的创建

        :param snippet_list (Array): 需要用到的初始化任务列表
        :param task_type (Integer): 任务类型

        :return : 任务ID
        """
        # 生成具体任务内容
        case_list = []
        for case_node_id in case_node_list:
            new_step = []
            for step in self.dao_server_core.get_step_by_case_node(
                    case_node_id=case_node_id,
                    os_type=self.plan_item["plan_type"]
            ):
                new_step += self.handle_one_step(copy.deepcopy(step))
            case_list.append({
                "caseNodeId": -1 * case_node_id,
                "nodeType": 2,
                "nodeName": "",
                "step": copy.deepcopy(new_step)
            })
        # 插入一条任务记录
        task_id = self.dao_core_cloud_task.insert_task(
            plan_id=self.plan_item["plan_id"],
            case_id=0,
            task_info=json.dumps({
                "caseFile": self.upload_case_file(case_list)
            }, ensure_ascii=False),
            type=task_type,
            retry_times=0
        )
        return task_id

    def create_execute_task(self):
        """
        生成所有执行任务

        :return: 执行任务列表
        """
        case_dict = {}
        case_id_set = set(self.plan_params["caseNodeIdList"])

        def trans_tree_to_list(children_tree, case_list=[]):
            """
            将树形结构扁平为数组形式
            """
            for item in children_tree:
                # 如果这个节点是一个手动任务则全部过滤
                if 1 == item["executionType"]:
                    continue
                node_item = copy.deepcopy(item)
                del node_item["children"]
                case_list.append(node_item)
                # 非子用例遍历到底
                if "children" in item and len(item["children"]) > 0:
                    trans_tree_to_list(item["children"], case_list)
                # 叶子结点进行完整用例处理
                else:
                    # 获取路径用例 ID
                    one_case_id_list = []
                    for node in case_list:
                        one_case_id_list.append(int(node["caseNodeId"]))
                    # 判断是否有需要创建任务用例 (路径用例 ID 与 需要创建任务的ID 集合取交集)
                    if len(list(set(one_case_id_list) & case_id_set)) > 0:
                        case_dict[str(item["caseNodeId"])] = {"case_list": copy.deepcopy(case_list)}
                case_list.pop()

        # 遍历用例树
        logger.info(
            "{} ID {} 开始获取执行任务".format(self.get_log_tag("create_execute_task"), self.plan_item["plan_id"]))
        trans_tree_to_list(self.case_tree)
        logger.info("{} ID {} 获取到预期拆分的执行任务 {} 个".format(self.get_log_tag(
            "execute"), self.plan_item["plan_id"], len(case_dict.keys())))
        if 0 == len(case_dict.keys()):
            return {
                "success": False,
                "msg": "找不到需要执行的用例"
            }
        # 获取前置任务列表
        setup_task_list = self.create_setup_task()
        logger.info("{} ID {} 获取到前置任务 List {}".format(self.get_log_tag(
            "create_execute_task"), self.plan_item["plan_id"], setup_task_list))
        got_after_task = False
        insert_list = []
        before_list = []
        setup_list = []
        after_list = []
        teardown_list = []
        # 循环生成所有执行任务
        for target_case_id in case_dict:
            logger.info("{} ID {} 准备处理用例ID {}".format(self.get_log_tag("create_execute_task"),
                                                            self.plan_item["plan_id"], target_case_id))
            case_info = self.handle_one_case(case_dict[target_case_id]["case_list"])

            def handle_case_dependcy_task_list(case_dependency, task_type):
                def handle_old_dependency_task_list(case_dependency_set, dependcy_task_list, task_type):
                    """
                    处理用例依赖的依赖任务列表 (旧形式, 测试片段)
                    """
                    if 0 == len(case_dependency_set):
                        return []
                    task_id_list = []
                    is_find = False
                    for task_item in dependcy_task_list:
                        if task_item["snippet_set"] == case_dependency_set:
                            logger.info("{} ID {} 处理用例ID {} 找到已有依赖任务 {}".format(
                                self.get_log_tag("handle_old_dependency_task_list"),
                                self.plan_item["plan_id"],
                                target_case_id,
                                task_item["task_id"]))
                            is_find = True
                            task_id_list.append(task_item["task_id"])
                    if not is_find:
                        logger.info("{} ID {} 处理用例ID {} 需要生成依赖任务".format(
                            self.get_log_tag("handle_case_dependcy_task_list"), self.plan_item["plan_id"],
                            target_case_id))
                        dependency_task_id = self.create_dependency_task(list(case_dependency_set), task_type)
                        task_id_list.append(dependency_task_id)
                        dependcy_task_list.append({
                            "snippet_set": case_dependency_set,
                            "task_id": dependency_task_id
                        })
                    return task_id_list

                def handle_new_dependency_task_list(case_dependency_set, dependcy_task_list, task_type):
                    """
                    处理用例依赖的依赖任务列表 (新形式, 节点片段)
                    """
                    if 0 == len(case_dependency_set):
                        return []
                    task_id_list = []
                    is_find = False
                    for task_item in dependcy_task_list:
                        if task_item["before_snippet_set"] == case_dependency_set:
                            logger.info("{} ID {} 处理用例ID {} 找到已有依赖任务 {}".format(
                                self.get_log_tag("handle_case_dependcy_task_list"),
                                self.plan_item["plan_id"],
                                target_case_id,
                                task_item["task_id"]))
                            is_find = True
                            task_id_list.append(task_item["task_id"])
                    if not is_find:
                        logger.info("{} ID {} 处理用例ID {} 需要生成依赖任务".format(
                            self.get_log_tag("handle_new_dependency_task_list"), self.plan_item["plan_id"],
                            target_case_id))
                        dependency_task_id = self.create_new_dependency_task(list(case_dependency_set), task_type)
                        task_id_list.append(dependency_task_id)
                        dependcy_task_list.append({
                            "before_snippet_set": case_dependency_set,
                            "task_id": dependency_task_id
                        })
                    return task_id_list

                if 1 == task_type:
                    if 0 == len(case_dependency["setup_snippet_set"]):
                        return handle_old_dependency_task_list(case_dependency["before_snippet_set"], before_list, 1)
                    else:
                        return handle_new_dependency_task_list(case_dependency["setup_snippet_set"], setup_list, 1)
                elif 4 == task_type:
                    if 0 == len(case_dependency["teardown_case_set"]):
                        return handle_old_dependency_task_list(case_dependency["after_snippet_set"], after_list, 4)
                    else:
                        return handle_new_dependency_task_list(case_dependency["teardown_case_set"], teardown_list, 4)
                else:
                    return []

            # 压入插入列表
            task_info = {
                "taskTimeout": self.plan_params["taskTimeout"],
                "setupList": setup_task_list,
                "beforeAllList": handle_case_dependcy_task_list(case_info, 1),
                "afterAllList": handle_case_dependcy_task_list(case_info, 4),
                "caseFile": case_info["case_file"],
                "isPendingManualCase": case_info["is_penging_manual"],
            }
            insert_list.append([
                int(self.plan_item["plan_id"]),
                int(target_case_id),
                json.dumps(task_info, ensure_ascii=False),
                0,
                0,
                current_timestamp(is_ms=False)
            ])
            if len(task_info["afterAllList"]) > 0:
                got_after_task = True
            logger.info("{} ID {} 处理用例ID {} 完成".format(self.get_log_tag("create_execute_task"),
                                                             self.plan_item["plan_id"], target_case_id))
        # 如果有后处理任务，则生成一个后置任务用于标识所有后置任务完成
        if got_after_task:
            self.dao_core_cloud_task.insert_task(
                plan_id=self.plan_item["plan_id"],
                case_id=0,
                task_info=json.dumps({}, ensure_ascii=False),
                type=3,
                retry_times=0
            )
        # 插入所有执行任务
        if len(insert_list) > 0:
            self.dao_core_cloud_task.insert_many_task(insert_list=insert_list)
        return {
            "success": True,
            "msg": ""
        }

    def handler_3_0_plan(self):
        """
        处理 QAMate2.0 格式计划
        """
        plan_info = {
            # "caseTree": self.bos.upload_from_json(
            #     json_object=case_tree,
            #     bos_path='/lazycloud/case/tree/{}_{}_{}.json'.format(
            #         plan_item["plan_id"], current_timestamp(), uuid.uuid4())
            # ),
            # "caseSnippet": self.bos.upload_from_json(
            #     json_object=case_snippet,
            #     bos_path='/lazycloud/case/snippet/{}_{}_{}.json'.format(
            #         plan_item["plan_id"], current_timestamp(), uuid.uuid4())
            # ),
        }
        logger.info(
            "{} ID {} 完成原用例树快照留存".format(self.get_log_tag("handler_3_0_plan"), self.plan_item["plan_id"]))
        # 处理环境变量
        plan_params = json.loads(self.plan_item["plan_params"])
        if "envParams" in plan_params and "envId" in plan_params["envParams"]:
            plan_params["envParams"]["envDetail"] = self.dao_server_core.get_env_by_id(
                env_id=plan_params["envParams"]["envId"])
        elif "envParams" in plan_params and "envDetail" in plan_params["envParams"]:
            if "appList" in plan_params["envParams"]["envDetail"]:
                plan_params["envParams"]["envDetail"]["appList"] = [
                    item for item in plan_params["envParams"]["envDetail"]["appList"] if
                    "envValue" in item and "appId" in item and item["envValue"] is not None
                ]
            else:
                plan_params["envParams"]["envDetail"]["appList"] = []
            if "paramList" in plan_params["envParams"]["envDetail"]:
                plan_params["envParams"]["envDetail"]["paramList"] = [
                    item for item in plan_params["envParams"]["envDetail"]["paramList"] if
                    "envValue" in item and "paramKey" in item and item["envValue"] is not None
                ]
            else:
                plan_params["envParams"]["envDetail"]["paramList"] = []
            if "serverList" in plan_params["envParams"]["envDetail"]:
                plan_params["envParams"]["envDetail"]["serverList"] = [
                    item for item in plan_params["envParams"]["envDetail"]["serverList"] if
                    "envValue" in item and "serverId" in item and item["envValue"] is not None
                ]
            else:
                plan_params["envParams"]["envDetail"]["serverList"] = []
            try:
                product_default_env = self.data_cloud_plan.get_default_dev(
                    module_id=self.plan_item["module_id"],
                    os_type=self.os_type)
                for env_app_item in product_default_env["appList"]:
                    for app_item in plan_params["envParams"]["envDetail"]["appList"]:
                        if env_app_item["appId"] == app_item["appId"]:
                            env_app_item["envValue"] = app_item["envValue"]
                            break
                for env_param_item in product_default_env["paramList"]:
                    for param_item in plan_params["envParams"]["envDetail"]["paramList"]:
                        if env_param_item["paramKey"] == param_item["paramKey"]:
                            env_param_item["envValue"] = param_item["envValue"]
                            break
                for env_server_item in product_default_env["serverList"]:
                    for server_item in plan_params["envParams"]["envDetail"]["serverList"]:
                        if env_server_item["serverId"] == server_item["serverId"]:
                            env_server_item["envValue"] = server_item["envValue"]
                            break
                plan_params["envParams"]["envDetail"] = product_default_env
            except:
                pass
        if "taskTimeout" not in plan_params:
            plan_params["taskTimeout"] = 25
        self.plan_params = plan_params
        self.plan_item["plan_params"] = json.dumps(plan_params, ensure_ascii=False)
        self.app_list = self.get_app_list()
        self.handle_task_ext()
        # 生成执行任务
        return plan_info, self.create_execute_task()
