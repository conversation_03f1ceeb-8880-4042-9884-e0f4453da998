# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 处理用例数据类

@File    :   cloud_case.py
@Time    :   2024-07-30
<AUTHOR>   wang<PERSON><PERSON><EMAIL>
"""
import copy
from src.models.dao.server.core import DaoServerCore
from src.util.type import get_os_tag

SETUP_VERSION_ID = 301


class ServiceDataCloudCase:
    """
    数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_server_core = DaoServerCore()

    def get_case_tree_by_case_root(self, case_root_id, os_type):
        """
        根据 case_root_id 获取用例树

        :param case_root_id: 用例根节点
        :param os_type: 端类型

        :return: Tree
        """
        case_tree = self.dao_server_core.get_case_by_case_root(case_root_id=case_root_id)
        os_type = get_os_tag(os_type=os_type)

        def handler_tree(case_tree, os_type):
            """
            将每个节点和系统端类型做初步预处理
            """
            for case in case_tree:
                case["step"] = []
                case["executionType"] = 0
                case["beforeAllInfo"] = []
                case["afterAllInfo"] = []
                if "extra" in case:
                    if "stepInfo" in case["extra"] and os_type in case["extra"]["stepInfo"]:
                        case["step"] = copy.deepcopy(case["extra"]["stepInfo"][os_type])
                        del case["extra"]["stepInfo"]
                    if "executionType" in case["extra"] and os_type in case["extra"]["executionType"]:
                        case["executionType"] = case["extra"]["executionType"][os_type]
                    if "beforeAllInfo" in case["extra"] and case["extra"]["beforeAllInfo"] is not None and os_type in case["extra"]["beforeAllInfo"]:
                        case["beforeAllInfo"] = case["extra"]["beforeAllInfo"][os_type]
                    if "afterAllInfo" in case["extra"] and case["extra"]["afterAllInfo"] is not None and os_type in case["extra"]["afterAllInfo"]:
                        case["afterAllInfo"] = case["extra"]["afterAllInfo"][os_type]
                    if "setupInfo" in case["extra"] and case["extra"]["setupInfo"] is not None and os_type in case["extra"]["setupInfo"]:
                        case["setupInfo"] = case["extra"]["setupInfo"][os_type]
                    if "teardownInfo" in case["extra"] and case["extra"]["teardownInfo"] is not None and os_type in case["extra"]["teardownInfo"]:
                        case["teardownInfo"] = case["extra"]["teardownInfo"][os_type]
                if "children" in case and isinstance(case["children"], list):
                    handler_tree(case["children"], os_type)
        handler_tree(case_tree, os_type)
        return case_tree

    def get_template_by_module_id(self, module_id, os_type):
        """
        根据 module_id 获取用例模板

        :param module_id: 模块 ID
        :param os_type: 端类型

        :return: List 模板列表
        """
        template_list = self.dao_server_core.get_template_by_module(module_id=module_id, os_type=os_type)
        snippet_list = []
        for template in template_list:
            snippet_list.append({
                "id": template["templateId"],
                "templateName": template["templateName"],
                "step": self.dao_server_core.get_step_by_case_node(
                    case_node_id=template["caseNodeId"],
                    os_type=os_type
                )
            })
        return snippet_list

    def get_login_setup_snippets(self, os_type):
        """
        根据 os_type 获取登陆模板

        :param os_type: 端类型

        :return: List 模板列表
        """
        app_list = self.dao_server_core.get_app_list_by_module(module_id=SETUP_VERSION_ID, os_type=os_type)
        template_list = self.dao_server_core.get_template_by_module(module_id=SETUP_VERSION_ID, os_type=os_type)
        snippet_list = []
        for template in template_list:
            snippet_item = {
                "id": template["templateId"],
                "templateName": template["templateName"],
                "step": []
            }
            for step in self.dao_server_core.get_step_by_case_node(
                case_node_id=template["caseNodeId"],
                os_type=os_type
            ):
                if (
                    1 == step["stepInfo"]["type"]
                    and step["stepInfo"]["params"]["type"] in ["closeApp", "launchApp", "clearApp", "authApp"]
                    and "id" in step["stepInfo"]["params"]["params"]
                ):
                    for app in app_list:
                        if app["appId"] == step["stepInfo"]["params"]["params"]["id"] and len(app["packageList"]) > 0:
                            step["stepInfo"]["params"]["params"]["packageName"] = app["packageList"][0]
                            break
                snippet_item["step"].append(step)
            snippet_list.append(copy.deepcopy(snippet_item))
        return snippet_list

    def get_login_setup_scheme(self, os_type):
        """
        根据 module_id 获取初始化 Scheme

        :param os_type: 端类型

        :return: List 模板列表
        """
        return self.dao_server_core.get_scheme_by_module_id(module_id=SETUP_VERSION_ID, os_type=os_type)
