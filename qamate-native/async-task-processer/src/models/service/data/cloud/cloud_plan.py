# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 处理计划数据类

@File    :   cloud_plan.py
@Time    :   2024-07-30
<AUTHOR>   wang<PERSON><PERSON><EMAIL>
"""
from datetime import datetime
from src.models.dao.server.core import DaoServerCore
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.util.time import compare_time_only_time_non_date


class ServiceDataCloudPlan:
    """
    数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_server_core = DaoServerCore()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()

    def is_plan_exclusive(self, plan_params):
        """
        根据计划参数判断是否是独占设备池计划

        :param plan_params: 计划创建参数

        :return: Boolean True(独占)
        """
        # 指定设备即一定为非独占
        if "deviceIdList" in plan_params["planParams"] and len(plan_params["planParams"]["deviceIdList"]) > 0:
            return False
        # 设定了 QPS 限制则一定为非独占
        if "qps" in plan_params["planParams"] and int(plan_params["planParams"]["qps"]) > 0:
            return False
        # 其余情况为独占
        return True

    def is_device_match_plan(self, plan_params, device_id):
        """
        根据计划参数判断设备是否可以在这个计划中执行

        :param plan_params: 计划创建参数
        :param device_id: 设备 ID

        :return: Boolean True(可以执行)
        """
        if self.is_plan_exclusive(plan_params):
            return True
        if "deviceIdList" in plan_params["planParams"] and len(plan_params["planParams"]["deviceIdList"]) > 0:
            return device_id in plan_params["planParams"]["deviceIdList"]
        if "qps" in plan_params["planParams"]:
            return True
        return False

    def is_qamate_2_0_plan(self, module_id):
        """
        根据模块 ID判断是否是 2.0 的 QAMate 计划

        :param module_id: 模块 ID

        :return: Boolean True(独占)
        """
        return module_id not in self.dao_server_core.get_3_0_white_list()

    def is_plan_can_execute_time(self, plan_params):
        """
        根据计划参数判断是否在可以执行的时间段内

        :param plan_params: 计划创建参数

        :return: Boolean True(可以执行)
        """
        # 如果没有指定时间段，则默认可以执行
        if "planParams" not in plan_params or "timePeriod" not in plan_params["planParams"]:
            return True
        start_time = datetime.strptime(plan_params["planParams"]["timePeriod"]["startTime"], "%H:%M")
        end_time = datetime.strptime(plan_params["planParams"]["timePeriod"]["endTime"], "%H:%M")
        now_time = datetime.now()
        # 开始时间比结束时间早，例如 13:00 ~ 14:00
        if compare_time_only_time_non_date(x=start_time, y=end_time):
            # 允许执行的时间为 start_time <= now_time <= end_time
            return (
                True == compare_time_only_time_non_date(x=start_time, y=now_time)
                and True == compare_time_only_time_non_date(x=now_time, y=end_time)
            )
        # 开始时间比结束时间晚，例如 20:00 ~ 10:00
        else:
            # 允许执行的时间为 now_time <= end_time || now_time >= start_time
            return (
                True == compare_time_only_time_non_date(x=now_time, y=end_time)
                or True == compare_time_only_time_non_date(x=start_time, y=now_time)
            )

    def ready_plan(self, plan_id, plan_info):
        """
        修改计划状态至待执行状态

        :param plan_id: 计划 ID
        :param plan_info: 计划中间信息数据

        :return: None
        """
        self.dao_core_cloud_plan.ready_plan(plan_id=plan_id, plan_info=plan_info)
        # try:
        #     self.dao_server_core.callback_3_0_plan_status(plan_id=plan_id, status=1, msg="")
        # except:
        #     pass

    def start_plan(self, plan_id):
        """
        修改计划状态 至 执行中

        :param plan_id: 计划 ID

        :return: None
        """
        self.dao_core_cloud_plan.start_plan(plan_id=plan_id)
        # try:
        #     self.dao_server_core.callback_3_0_plan_status(plan_id=plan_id, status=2, msg="")
        # except:
        #     pass

    def end_plan(self, plan_id):
        """
        修改计划状态 至 结束

        :param plan_id: 计划 ID

        :return: None
        """
        self.dao_core_cloud_plan.end_plan(plan_id=plan_id)
        # try:
        #     self.dao_server_core.callback_3_0_plan_status(plan_id=plan_id, status=3, msg="")
        # except:
        #     pass

    def except_plan(self, plan_id, exception_msg):
        """
        修改计划状态至异常状态

        :param plan_id: 计划 ID
        :param exception_msg: 计划异常原因

        :return: None
        """
        self.dao_core_cloud_plan.except_plan(plan_id=plan_id, exception_msg=exception_msg)
        # try:
        #     self.dao_server_core.callback_3_0_plan_status(plan_id=plan_id, status=4, msg=exception_msg)
        # except:
        #     pass

    def get_default_dev(self, module_id, os_type):
        """
        获取默认环境配置

        :param module_id: 模块 ID
        :param os_type: 系统类型

        :return: 环境 Dict
        """
        env_list = self.dao_server_core.get_env_list(module_id=module_id, os_type=os_type)
        default_env_id = env_list[0]["envId"]
        for env in env_list:
            if env["envName"] == "默认环境":
                default_env_id = env["envId"]
                break
        return self.dao_server_core.get_env_by_id(env_id=default_env_id)

    def get_un_pending_plan_by_id_list(self, plan_list):
        """
        根据 ID 列表返回对应的计划

        :param id_list: ID List

        :return: 查询结果集。
        """
        return [item["id"] for item in self.dao_core_cloud_plan.get_un_pending_plan_by_id_list(id_list=plan_list)]
