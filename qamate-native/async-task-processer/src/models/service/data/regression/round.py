# -*- encoding: utf-8 -*-
"""
@Desc    :   Regression 处理轮次数据类

@File    :   round.py
@Time    :   2024-07-30
<AUTHOR>   wang<PERSON><PERSON><PERSON><PERSON>@baidu.com
"""
from src.models.dao.db.core_regression_round import DaoDBRegressionRound


class ServiceDataRegressionRound:
    """
    数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_regression_round = DaoDBRegressionRound()

    def get_end_round_list(self, round_list):
        """
        根据 round_list 获取其中哪些已经完成

        :param round_list: 轮次 ID 列表

        :return: 完成轮次的 ID 列表
        """
        return [item["id"] for item in self.dao_regression_round.get_end_round(id_list=round_list)]
