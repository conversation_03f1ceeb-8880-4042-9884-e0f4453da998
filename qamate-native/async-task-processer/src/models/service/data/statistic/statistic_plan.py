# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 计划数据处理类

@File    :   statistic_plan.py
@Time    :   2024-11-14
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
from src.models.dao.db.core_regression_plan import DaoDBRegressionPlan


class ServiceDataStatisticPlan:
    """
    计划数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象
        """
        self.dao_core_regression_plan = DaoDBRegressionPlan()

    def get_plan_list_by_module_id_and_time(self, module_id, start_time, end_time):
        """
        根据业务线与时间区间获取过滤测试计划
        """
        plan_list = {
            "del_plan_list": [],
            "final_plan_list": []
        }
        search_plan_list = self.dao_core_regression_plan.get_plan_by_module_id_and_type_and_create_time(
            module_id=module_id, plan_type=1, start_time=start_time, end_time=end_time)
        # 拆分测试计划，将已删除的测试计划进行记录清理
        for plan in search_plan_list:
            plan_info = {
                "plan_id": plan["id"],
                "plan_name": plan["name"],
                "plan_create_time": plan["create_time"],
                "node_tree": plan["node_tree"]
            }
            if plan["is_del"] == 0:
                plan_list["final_plan_list"].append(plan_info)
            else:
                plan_list["del_plan_list"].append(plan_info)
        # 由于直接通过 plan_type 进行了区分，不需要再通过测试计划名称去进行筛选
        return plan_list
