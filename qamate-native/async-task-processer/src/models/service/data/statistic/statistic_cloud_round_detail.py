# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 轮次自动化详情数据处理类

@File    :   statistic_cloud_round_detail.py
@Time    :   2024-11-14
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
from src.lib.logger import logger
from src.models.dao.db.core_regression_cloud_round_detail import DaoDBRegressionCloudRoundDetail


class ServiceDataStatisticCloudRoundDetail:
    """
    轮次自动化详情数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象
        """
        self.dao_core_regression_cloud_round_detail = DaoDBRegressionCloudRoundDetail()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.data.statistics.statistic_cloud_round_detail.{}]".format(method_name)

    def get_execute_data(self, case_ids, cloud_plan_id):
        """
        解析自动化详情数据
        """
        # 获取执行结果详情
        execute_res = {
            "execute_num": 0,
            "execute_success_num": 0,
            "execute_failed_num": 0,
            "execute_exception_num": 0,
            "execute_cancel_num": 0,
            "execute_running_num": 0,
            "execute_extra_num": 0
        }
        detail_list = self.dao_core_regression_cloud_round_detail.get_detail_by_cloud_plan_id(
            cloud_plan_id=cloud_plan_id)
        if len(detail_list) == 0:
            logger.warning("{} 云端计划 id:{} 未找到对应的云端执行详情".format(
                self.get_log_tag("get_execute_data"), cloud_plan_id))
            return execute_res
         # 需要对于用例去重
        case_node_set = set()
        # 获取该自动化结果
        if len(detail_list) == 0:
            return execute_res
        for detail in detail_list:
            if detail["case_node_id"] in case_ids:
                if detail["case_node_id"] in case_node_set:
                    continue
                else:
                    case_node_set.add(detail["case_node_id"])
                    if detail["status"] == 2 or detail["status"] == 5:
                        execute_res["execute_success_num"] += 1
                    elif detail["status"] == 3:
                        execute_res["execute_failed_num"] += 1
                    elif detail["status"] == 4:
                        execute_res["execute_exception_num"] += 1
                    elif detail["status"] == 6:
                        execute_res["execute_cancel_num"] += 1
                    elif detail["status"] == 1:
                        execute_res["execute_running_num"] += 1
                    else:
                        execute_res["execute_extra_num"] += 1
        execute_res["execute_num"] = len(case_node_set)
        return execute_res
