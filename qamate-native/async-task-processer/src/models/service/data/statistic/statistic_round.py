# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 轮次数据处理类

@File    :   statistic_round.py
@Time    :   2024-11-14
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
from src.lib.logger import logger
from src.models.dao.db.core_regression_round import DaoDBRegressionRound
from src.models.dao.db.core_regression_cloud_round_detail import DaoDBRegressionCloudRoundDetail
from src.models.service.data.statistic.statistic_common import ServiceDataStatisticCommon
from src.models.dao.server.core import DaoServerCore


class ServiceDataStatisticRound:
    """
    计划数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象
        """
        self.dao_core_regression_round = DaoDBRegressionRound()
        self.dao_core_regression_cloud_round_detail = DaoDBRegressionCloudRoundDetail()
        self.data_statistic_common = ServiceDataStatisticCommon()
        self.dao_server_core = DaoServerCore()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.data.statistics.statistic_round.{}]".format(method_name)

    def get_round_detail_by_id(self, round_id):
        """
        根据轮次ID获取结果树
        """
        round_info = self.dao_server_core.get_round_detail_by_round_id(round_id=round_id)["caseTree"]
        return round_info

    def get_statistic_data(self, tree, os_tag):
        """
        统计概览数据
        """
        # 将叶子节点都写入到一个列表中
        base_res = {
            "auto": [],
            "half": [],
            "manual": []
        }
        if tree is None:
            return base_res
        container = []
        container.append(tree)
        self.get_case_type(container, os_tag, -1, base_res)
        return base_res

    def get_case_type(self, case_tree, os_tag, now_type, result_case_map):
        """
        扫描用例树获取节点类型
        """
        if case_tree is None:
            return
        for case in case_tree:
            if os_tag not in case["extra"]["executionType"]:
                logger.error("{} 用例列表 {} 不存在 {} 的执行类型".format(
                    self.get_log_tag("get_case_type"), case["caseNodeId"], os_tag))
                continue
            execution_type = case["extra"]["executionType"][os_tag]
            child_node_type = now_type
            # executionType 1 手动 2 自动 3 自动生成 4 半自动
            # childNodeType 0-自动 1-半自动 2-人工
            if -1 == child_node_type:
                if execution_type == 1:
                    child_node_type = 2
                elif execution_type == 2:
                    child_node_type = 0
                elif execution_type == 4:
                    child_node_type = 1
            else:
                if execution_type == 1:
                    child_node_type = 2
                elif execution_type == 4 and child_node_type == 0:
                    child_node_type = 1
            if len(case["children"]) != 0:
                # 非叶子结点，递归孩子结点
                self.get_case_type(case["children"], os_tag, child_node_type, result_case_map)
            else:
                # 叶子结点，同步结果进入 map
                if execution_type == 0:
                    result_case_map["manual"].append(case["caseNodeId"])
                elif child_node_type == 0:
                    result_case_map["auto"].append(case["caseNodeId"])
                elif child_node_type == 1:
                    result_case_map["half"].append(case["caseNodeId"])
                elif child_node_type == 2:
                    result_case_map["manual"].append(case["caseNodeId"])
                elif child_node_type == -1:
                    result_case_map["manual"].append(case["caseNodeId"])

    def get_link_case_node(self, node):
        """
        获取绑定的用例节点列表
        """
        case_node_info = {
            "link_case_node_list": [],
            "case_node_id": node["caseNodeId"]
        }
        extra = node["extra"]
        link_list = extra["linkList"]
        if len(link_list) > 0:
            link_case_node_list = []
            for link_info in link_list:
                if "path" not in link_info or link_info["name"] != "UI自动化":
                    continue
                path = link_info["path"]
                band_case_node_id = path.split("=")[1].split("&")[0]
                band_case_node_id = int(band_case_node_id)
                link_case_node_list.append(band_case_node_id)
            case_node_info["link_case_node_list"] = link_case_node_list
        return case_node_info

    def get_fork_case_node(self, node):
        """
        获取绑定的原用例
        """
        case_node_info = {
            "case_node_id": node["caseNodeId"],
            "fork_case_node_id": 0
        }
        extra = node["extra"]
        case_node_info["fork_case_node_id"] = extra["forkCaseNodeId"]
        return case_node_info

    def get_link_data(self, case_tree, case_list, execute_type, os_tag):
        """
        获取绑定信息
        """
        link_res = []
        fork_res = []
        container = []
        container.append(case_tree)
        while len(container) > 0:
            node = container.pop(0)
            if len(node["children"]) != 0:
                container += node["children"]
            else:
                if node["caseNodeId"] in case_list:
                    # 叶子结点进行绑定检测
                    # 1.只对进行了签章的用例进行绑定检测
                    if (self.sign_check(node, os_tag)):
                        # 2.1 如果是手工用例则获取绑定用例列表
                        if (execute_type == "manual"):
                            case_link_info = self.get_link_case_node(node)
                            if len(case_link_info) > 0:
                                link_res.append(case_link_info)
                        # 2.2 如果是自动化用例则获取 fork_case_node_id
                        if (execute_type == "auto"):
                            fork_node_info = self.get_fork_case_node(node)
                            fork_res.append(fork_node_info)
        if execute_type == "manual":
            return link_res
        else:
            return fork_res

    def sign_check(self, node, os_tag):
        """
        是否进行过签章校验
        """
        sign_list = node["extra"]["signInfo"][os_tag]["regressionSignList"]
        if sign_list is None or len(sign_list) == 0:
            return False
        else:
            return True

    def check_color_tree(self, module_id, node, tag):
        """
        校验是否需要染色树
        """
        if self.data_statistic_common.check_is_special_product(module_id) == True:
            # 进行树染色
            self.color_link_tree(node, tag)

    def color_link_tree(self, node, tag):
        """
        染色签章树，解决只绑定分支节点的问题
        """
        # 如果当前存在 link ,进行下层染色
        link_path = self.get_ui_link(node)
        if link_path != "":
            # 没有颜色,下传
            tag = link_path
        # 把下层都染色
        if link_path == "" and tag != "":
            extra = node["extra"]
            link_list = extra["linkList"]
            link_info = {
                "name": "UI自动化",
                "path": tag
            }
            link_list.append(link_info)
        if len(node["children"]) > 0:
            # 存在子节点
            for child in node["children"]:
                self.color_link_tree(child, tag)

    def get_ui_link(self, node):
        """
        获取绑定 UI自动化 的链接
        """
        extra = node["extra"]
        link_list = extra["linkList"]
        if len(link_list) > 0:
            for link_info in link_list:
                if link_info["name"] == "UI自动化":
                    return link_info["path"]
        return ""
