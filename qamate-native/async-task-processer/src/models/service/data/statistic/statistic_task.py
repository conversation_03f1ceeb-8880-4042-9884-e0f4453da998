# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 任务数据处理类

@File    :   statistic_task.py
@Time    :   2024-11-14
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
from src.models.dao.db.core_regression_task import DaoDBRegressionTask


class ServiceDataStatisticTask:
    """
    任务数据处理类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象
        """
        self.dao_core_regression_task = DaoDBRegressionTask()

    def get_task_list_by_plan_id(self, plan_list):
        """
        获取计划对应的任务
        """
        task_list = self.dao_core_regression_task.get_task_by_plan_id_in(plan_id=plan_list)
        task_res = []
        for task in task_list:
            task_info = {}
            task_info["id"] = task["id"]
            task_info["os_type"] = task["os_type"]
            task_info["tree_node_id"] = task["tree_node_id"]
            task_res.append(task_info)
        return task_res
