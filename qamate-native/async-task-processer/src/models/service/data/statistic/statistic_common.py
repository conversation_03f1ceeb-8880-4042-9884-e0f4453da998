# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 报表通用能力类

@File    :   statistic_common.py
@Time    :   2024-11-14
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
from src.util.time import current_timestamp, get_timestamp_with_gap


class ServiceDataStatisticCommon:
    """
    报表通用能力类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象
        """
        self.DEFAULT_PRODUCT_INFO = [
            {
                "product_id": 18,
                "product_name": "手百基础"
            }, {
                "product_id": 85,
                "product_name": "百度APP-内核"
            }, {
                "product_id": 104,
                "product_name": "基础技术&性能"
            }, {
                "product_id": 1,
                "product_name": "Feed-视频"
            }, {
                "product_id": 2,
                "product_name": "Feed-核心"
            }, {
                "product_id": 9,
                "product_name": "小说客户端"
            }, {
                "product_id": 23,
                "product_name": "品牌前端广告"
            }, {
                "product_id": 26,
                "product_name": "搜索客户端"
            }, {
                "product_id": 69,
                "product_name": "小程序客户端"
            }, {
                "product_id": 8,
                "product_name": "贴吧"
            }, {
                "product_id": 29,
                "product_name": "文库APP"
            }, {
                "product_id": 24,
                "product_name": "网盟-SDK"
            }, {
                "product_id": 37,
                "product_name": "百度极速版"
            }, {
                "product_id": 12,
                "product_name": "商业原生广告"
            }, {
                "product_id": 21,
                "product_name": "newapp"
            }, {
                "product_id": 15,
                "product_name": "IM"
            }
        ]
        self.DEFAULT_SPECIAL_PRODUCT_IDS = [85]

    def check_is_special_product(self, module_id):
        """
        判断是否为特殊处理的业务线
        """
        return module_id in self.DEFAULT_SPECIAL_PRODUCT_IDS

    def get_default_product_info(self):
        """
        获取默认业务线信息
        """
        return self.DEFAULT_PRODUCT_INFO

    def get_default_date_range(self):
        """
        获取默认时间范围
        """
        # 初始化扫描时间范围 30 天
        end_time = current_timestamp(is_ms=False)
        start_time = get_timestamp_with_gap(end_time, 30)
        return {
            "start_time": start_time,
            "end_time": end_time
        }
