# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 集成回归统计

@File    :   case_daily_monitor.py
@Time    :   2024-10-23
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
import json
import traceback
from src.lib.logger import logger
from src.util.time import current_timestamp
from src.util.type import get_os_tag, get_os_type
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_device_task import DaoDBCoreDeviceTask
from src.models.dao.db.core_regression_plan_statistic import DaoDBRegressionPlanStatistic
from src.models.dao.db.core_cloud_device_task import DaoDBCoreDeviceTask
from src.models.dao.db.core_regression_round import DaoDBRegressionRound
from src.models.dao.db.core_regression_cloud_round import DaoDBRegressionCloudRound
from src.models.dao.db.core_regression_cloud_round_statistic import DaoDBRegressionCloudRoundStatistic
from src.models.service.data.statistic.statistic_common import ServiceDataStatisticCommon
from src.models.service.data.statistic.statistic_plan import ServiceDataStatisticPlan
from src.models.service.data.statistic.statistic_task import ServiceDataStatisticTask
from src.models.service.data.statistic.statistic_round import ServiceDataStatisticRound
from src.models.service.data.statistic.statistic_cloud_round_detail import ServiceDataStatisticCloudRoundDetail
from src.models.dao.redis.redis_lock import DaoRedisLock


class ServicePageTaskDailyMonitor:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_device_task = DaoDBCoreDeviceTask()
        self.dao_core_regression_plan_statistic = DaoDBRegressionPlanStatistic()
        self.dao_core_regression_round = DaoDBRegressionRound()
        self.dao_core_regression_cloud_round = DaoDBRegressionCloudRound()
        self.dao_core_regression_cloud_round_statistic = DaoDBRegressionCloudRoundStatistic()
        self.data_statistic_common = ServiceDataStatisticCommon()
        self.data_statistic_plan = ServiceDataStatisticPlan()
        self.data_statistic_task = ServiceDataStatisticTask()
        self.data_statistic_round = ServiceDataStatisticRound()
        self.data_statistic_cloud_round_detail = ServiceDataStatisticCloudRoundDetail()
        self.dao_redis_lock = DaoRedisLock()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.statistics.task_daily_monitor.{}]".format(method_name)

    def get_default_res(self, module_id, module_name, timestamp):
        """
        构建默认的结果
        """
        res = {
            "base": {
                "module_id": module_id,  # 业务线id
                "module_name": module_name,  # 业务线名称
                "plan_id": 0,  # 计划编号
                "plan_name": "",  # 计划名称
                "plan_create_time": 0,  # 测试计划创建时间
                "task_id": 0,  # 任务编号
                "tree_node_id": 0,  # 目录节点编号
                "tree_node_name": "",  # 目录节点名称
                "round_id": 0,  # 最近执行的轮次编号
                "os_type": 0,  # 端类型
                "os_tag": "",  # 端标签
                "cloud_plan_id": 0,  # 云端计划编号
                "cloud_plan_create_time": 0,  # 云端计划创建时间
                "statistic_update_time": timestamp  # 统计更新时间
            },
            "execute": {
                "execute_case_num": 0,  # 自动化小助手执行用例数目
                "execute_success_num": 0,  # 用例执行成功数
                "execute_fail_num": 0,  # 用例执行失败数
                "execute_cancel_num": 0,  # 用例取消数目
                "execute_except_num": 0,  # 用例异常数目
                "execute_running_num": 0,  # 执行中用例数目
                "start_time": 0,  # 执行开始时间
                "end_time": 0,  # 执行结束时间
                "device_list": set()  # 执行设备列表
            }
        }
        return res

    def get_plan_res_by_single_os(self, module_id, module_name, timestamp, os_type,
                                  plan_id, plan_name, plan_create_time):
        """
        构建默认的测试计划层面分端结果
        """
        res = {
            "base": {
                "module_id": module_id,  # 业务线id
                "module_name": module_name,  # 业务线名称
                "plan_id": plan_id,  # 计划编号
                "plan_name": plan_name,  # 计划名称
                "os_type": os_type,  # 端类型
                "os_tag": get_os_tag(os_type=os_type),  # 端标签
                "plan_create_time": plan_create_time,  # 测试计划创建时间
                "statistic_update_time": timestamp  # 统计更新时间
            },
            "execute": {
                "execute_case_num": 0,  # 自动化小助手执行用例数目
                "execute_success_num": 0,  # 用例执行成功数
                "execute_fail_num": 0,  # 用例执行失败数
                "execute_cancel_num": 0,  # 用例取消数目
                "execute_except_num": 0,  # 用例异常数目
                "execute_running_num": 0,  # 执行中用例数目
                "time_ranges": [],  # 执行时间范围列表
                "device_list": set(),  # 执行设备列表
                "device_num": 0  # 执行设备数目（去重）
            }
        }
        return res

    def get_default_plan_res(self, module_id, module_name, timestamp, plan_id, plan_name, plan_create_time):
        """
        获取双端测试计划结果
        """
        plan_res = {
            "android": self.get_plan_res_by_single_os(
                module_id,
                module_name,
                timestamp,
                get_os_type(os_tag="android"),
                plan_id,
                plan_name,
                plan_create_time
            ),
            "ios": self.get_plan_res_by_single_os(
                module_id,
                module_name,
                timestamp,
                get_os_type(os_tag="ios"),
                plan_id,
                plan_name,
                plan_create_time
            )
        }
        return plan_res

    def add_plan_res(self, plan_res, module_res, time_info):
        """
        将原子结果并入计划层面
        """
        plan_single_os_res = plan_res[module_res["base"]["os_tag"]]
        current_device_list = plan_single_os_res["execute"]["device_list"]
        plan_single_os_res["execute"]["execute_case_num"] += module_res["execute"]["execute_case_num"]
        plan_single_os_res["execute"]["execute_success_num"] += module_res["execute"]["execute_success_num"]
        plan_single_os_res["execute"]["execute_fail_num"] += module_res["execute"]["execute_fail_num"]
        plan_single_os_res["execute"]["execute_cancel_num"] += module_res["execute"]["execute_cancel_num"]
        plan_single_os_res["execute"]["execute_except_num"] += module_res["execute"]["execute_except_num"]
        plan_single_os_res["execute"]["execute_running_num"] += module_res["execute"]["execute_running_num"]
        plan_single_os_res["execute"]["time_ranges"].append(time_info)
        plan_single_os_res["execute"]["device_list"] = current_device_list | (module_res["execute"]["device_list"])
        return plan_res

    def commit_plan_res(self, plan_res):
        """
        计划层面结果处理与提交
        """
        # 主要处理两个内容：设备数目 和 执行时长
        for os_tag in ["android", "ios"]:
            plan_sigle_os_res = plan_res[os_tag]
            plan_sigle_os_res["execute"]["device_num"] = len(plan_sigle_os_res["execute"]["device_list"])
            # 计算执行时长
            plan_sigle_os_res["execute"]["execute_time"] = self.execute_plan_execute_time(
                plan_sigle_os_res["execute"]["time_ranges"])
            # 提交到数据库
            self.update_or_insert_plan(plan_sigle_os_res)

    def execute_plan_execute_time(self, time_ranges):
        """
        进行计划级别的执行时长计算
        """
        time_list = []
        sum_execute_time = 0
        if len(time_ranges) == 0:
            return 0
        # 先将未结束的任务进行去除
        for time_range in time_ranges:
            if time_range["end_time"] != 0:
                time_list.append(time_range)
        # 根据开始时间进行排序
        time_list = sorted(time_list, key=lambda x: x["start_time"])
        # 构建滑动窗口
        index_l = time_list[0]["start_time"]
        index_r = time_list[0]["end_time"]
        for time_range in time_list:
            target_l = time_range["start_time"]
            target_r = time_range["end_time"]
            if index_l == target_l and index_r == target_r:
                continue
            if index_r > target_l:
                if index_r < target_r:
                    index_r = target_r
            else:
                sum_execute_time += index_r - index_l
                index_l = target_l
                index_r = target_r
        sum_execute_time += index_r - index_l
        return sum_execute_time

    def create_execute_time_range(self, start_time, end_time):
        """
        生成执行时间范围
        """
        time_info = {
            "start_time": start_time,
            "end_time": end_time
        }
        return time_info

    def check_exist_record(self, cloud_plan_id_list):
        """
        检查是否存在记录
        """
        # 获取存在的云端任务
        exist_record_cloud_plan_id = []
        exist_record = self.dao_core_regression_cloud_round_statistic.get_finished_record(cloud_plan_id_list)
        if exist_record is None:
            return exist_record_cloud_plan_id
        for item in exist_record:
            exist_record_cloud_plan_id.append(item["cloud_plan_id"])
        return exist_record_cloud_plan_id

    def analysis_node_tree(self, node_tree):
        """
        拆借计划涉及目录节点
        """
        node_tree_map = {}
        for item in node_tree:
            # 开始扫描叶子结点，放入 id 和 name
            container = []
            container.append(item)
            while len(container) > 0:
                tmp = container.pop(0)
                if tmp["children"] is not None and len(tmp["children"]) != 0:
                    for child in tmp["children"]:
                        container.append(child)
                else:
                    node_tree_map[tmp["nodeId"]] = tmp["nodeName"]
        return node_tree_map

    def get_execute_device_num(self, cloud_plan_id):
        """
        获取执行设备数量
        """
        # 获取执行设备
        device_ids = set()
        device_list = self.dao_core_device_task.get_device_list_by_plan_id(cloud_plan_id=cloud_plan_id)
        for item in device_list:
            device_ids.add(item["device_id"])
        return device_ids

    def update_or_insert_record(self, record):
        """
        更新或写入任务数据
        """
        # 校验记录存在
        # 查询条件 cloud_plan_id
        find_res = self.dao_core_regression_cloud_round_statistic.get_record_by_cloud_plan_id(
            cloud_plan_id=record["base"]["cloud_plan_id"])
        if find_res is None:
            # 当前业务线不存在记录，进行插入
            logger.info("{} 插入记录:{}".format(self.get_log_tag("insert"), record))
            self.dao_core_regression_cloud_round_statistic.insert_record(record)
        else:
            record_id = find_res["id"]
            # 当前业务线存在既有记录，进行更新
            logger.info("{} 更新记录:{}".format(self.get_log_tag("update"), record))
            self.dao_core_regression_cloud_round_statistic.update_record(record, record_id)

    def update_or_insert_plan(self, plan):
        """
        更新或写入计划维度数据
        """
        # 校验记录存在
        # 查询条件 plan_id
        find_res = self.dao_core_regression_plan_statistic.get_record_by_plan_id_and_os(
            plan_id=plan["base"]["plan_id"], os_type=plan["base"]["os_type"])
        if find_res is None:
            # 当前业务线不存在记录，进行插入
            logger.info("{} 插入记录:{}".format(self.get_log_tag("insert"), plan))
            self.dao_core_regression_plan_statistic.insert_record(plan)
        else:
            record_id = find_res["id"]
            # 当前业务线存在既有记录，进行更新
            logger.info("{} 更新记录:{}".format(self.get_log_tag("update"), plan))
            self.dao_core_regression_plan_statistic.update_record(plan, record_id)

    def execute(self):
        """
        执行主函数
        """
        logger.info("{} 开始执行".format(self.get_log_tag("execute")))
        # 初始化日期
        date_range = self.data_statistic_common.get_default_date_range()
        # 获取该段日期内对应业务线的测试计划
        for product_info in self.data_statistic_common.get_default_product_info():
            start_time = current_timestamp()
            # 开始抢占分布式锁准备处理池子
            redis_key = "task_daily_monitor_product_{}".format(product_info["product_id"])
            is_locked = False
            module_id = product_info["product_id"]
            module_name = product_info["product_name"]
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} 任务执行采集 业务线 id {} 定时采集器抢占分布式锁失败".format(
                        self.get_log_tag("execute"), module_id))
                    continue
                # 获取集成测试计划
                total_plan_list = self.data_statistic_plan.get_plan_list_by_module_id_and_time(
                    module_id=module_id, start_time=date_range["start_time"], end_time=date_range["end_time"])
                final_plan_id_list = total_plan_list["final_plan_list"]
                if len(final_plan_id_list) == 0:
                    logger.warning("{} 业务线 id:{} 未找到集成测试计划".format(
                        self.get_log_tag("execute"), module_id))
                    continue
                # 获取对应的下属 task
                for plan_info in final_plan_id_list:
                    plan_id = plan_info["plan_id"]
                    plan_name = plan_info["plan_name"]
                    plan_create_time = plan_info["plan_create_time"]
                    plan_node_tree = json.loads(plan_info["node_tree"])
                    node_tree_map = self.analysis_node_tree(plan_node_tree)
                    # 获取默认的计划结果
                    plan_res = self.get_default_plan_res(
                        module_id, module_name, start_time, plan_id, plan_name, plan_create_time)
                    plan_list = [plan_id]
                    task_res = self.data_statistic_task.get_task_list_by_plan_id(plan_list=plan_list)
                    if len(task_res) == 0:
                        logger.warning("{} 业务线 id:{} 计划 id:{} 未找到对应的任务".format(
                            module_id, self.get_log_tag("execute"), plan_id))
                        continue
                    # 对于每个 task 获取执行完的最新的任务
                    for task_info in task_res:
                        task_id = task_info["id"]
                        task_os = task_info["os_type"]
                        tree_node_id = task_info["tree_node_id"]
                        round_list = self.dao_core_regression_round.get_round_by_task_id(task_id=task_id)
                        os_tag = get_os_tag(os_type=task_os)
                        if len(round_list) == 0:
                            logger.warning("{} 业务线 id:{} 计划 id:{} 任务 id:{} 未找到对应的轮次 或 轮次异常".format(
                                self.get_log_tag("execute"), module_id, plan_id, task_id))
                            continue
                        for round_info in round_list:
                            round_id = round_info["id"]
                            # round_status = round_info["status"]
                            # round_end_time = round_info["end_time"]
                            # 如果当前轮次已结束 并 结束超过 5 天
                            # if round_status == 1 and (current_timestamp(is_ms=False) - round_end_time > 3600 * 24 * 5):
                            #     logger.info("{} 当前轮次:{} 已结束 并 结束时间超过 5 天".format(
                            #         self.get_log_tag("execute"), round_id))
                            #     continue
                            # 获取轮次对应的所有云端任务
                            cloud_round_list = self.dao_core_regression_cloud_round.get_all_cloud_round_by_round_id(
                                round_id=round_id)
                            if len(cloud_round_list) == 0:
                                logger.warning("{} 业务线 id:{} 计划 id:{} 任务 id:{} 轮次 id:{} 未找到对应的云端轮次".format(
                                    self.get_log_tag("execute"), module_id, plan_id, task_id, round_id))
                                continue
                            # 获取轮次详情
                            round_detail = self.data_statistic_round.get_round_detail_by_id(round_id=round_id)
                            # 扫描获取自动化用例列表，半自动用例列表
                            base_res = self.data_statistic_round.get_statistic_data(tree=round_detail, os_tag=os_tag)
                            auto_case_ids = base_res["auto"]
                            half_case_ids = base_res["half"]
                            # 获取 cloud_plan_id 列表，并进行校验是否需要再次同步
                            cloud_plan_id_list = []
                            for cloud_round_info in cloud_round_list:
                                cloud_plan_id = cloud_round_info["cloud_plan_id"]
                                cloud_plan_id_list.append(cloud_plan_id)
                            # exist_plan_list = self.check_exist_record(cloud_plan_id_list)
                            for cloud_round_info in cloud_round_list:
                                # 对于每一个绑定任务，获取对应的云端执行信息
                                cloud_plan_id = cloud_round_info["cloud_plan_id"]
                                cloud_plan_start_time = cloud_round_info["create_time"]
                                cloud_plan_end_time = cloud_round_info["end_time"]
                                cloud_plan_create_time = self.dao_core_cloud_plan.get_plan_info_by_id(
                                    plan_id=cloud_plan_id)
                                # 对 cloud_plan_id 存在进行校验
                                # 优化条件：cloud_plan_id 存在 并且 执行时长大于 0（代表已结束）
                                # if cloud_plan_id in exist_plan_list:
                                #     logger.info("{} 轮次:{} 云端任务 {} 已存在并结束".format(
                                #         self.get_log_tag("execute"), round_id, cloud_plan_id))
                                #     continue
                                module_res = self.get_default_res(module_id, module_name, date_range["end_time"])
                                auto_execute_res = self.data_statistic_cloud_round_detail.get_execute_data(
                                    case_ids=auto_case_ids, cloud_plan_id=cloud_plan_id)
                                half_execute_res = self.data_statistic_cloud_round_detail.get_execute_data(
                                    case_ids=half_case_ids, cloud_plan_id=cloud_plan_id)

                                # 获取执行设备数目
                                device_list = self.get_execute_device_num(cloud_plan_id)
                                # 拼接数据更新 / 入库
                                module_res["base"]["module_id"] = module_id
                                module_res["base"]["module_name"] = module_name
                                module_res["base"]["plan_id"] = plan_id
                                module_res["base"]["plan_name"] = plan_name
                                module_res["base"]["plan_create_time"] = plan_create_time
                                module_res["base"]["task_id"] = task_id
                                module_res["base"]["tree_node_id"] = tree_node_id
                                module_res["base"]["tree_node_name"] = node_tree_map[tree_node_id]
                                module_res["base"]["round_id"] = round_id
                                module_res["base"]["os_type"] = task_os
                                module_res["base"]["os_tag"] = get_os_tag(os_type=task_os)
                                module_res["base"]["cloud_plan_id"] = cloud_plan_id
                                module_res["base"]["cloud_plan_create_time"] = cloud_plan_create_time["create_time"]
                                module_res["base"]["statistic_update_time"] = date_range["end_time"]
                                # 任务执行信息
                                module_res["execute"]["execute_case_num"] = auto_execute_res["execute_num"]
                                module_res["execute"]["execute_case_num"] += half_execute_res["execute_num"]
                                module_res["execute"]["execute_auto_num"] = auto_execute_res["execute_num"]
                                module_res["execute"]["execute_success_num"] = auto_execute_res["execute_success_num"]
                                module_res["execute"]["execute_fail_num"] = auto_execute_res["execute_failed_num"]
                                module_res["execute"]["execute_except_num"] = auto_execute_res["execute_exception_num"]
                                module_res["execute"]["execute_running_num"] = auto_execute_res["execute_running_num"]
                                module_res["execute"]["execute_success_num"] += half_execute_res["execute_success_num"]
                                module_res["execute"]["execute_fail_num"] += half_execute_res["execute_failed_num"]
                                module_res["execute"]["execute_except_num"] += half_execute_res["execute_exception_num"]
                                module_res["execute"]["execute_running_num"] += half_execute_res["execute_running_num"]
                                module_res["execute"]["start_time"] = cloud_plan_start_time
                                module_res["execute"]["end_time"] = cloud_plan_end_time
                                module_res["execute"]["device_num"] = len(device_list)
                                module_res["execute"]["device_list"] = device_list
                                # 更新或写入记录
                                self.update_or_insert_record(module_res)
                                # 写入单端测试计划层面结果
                                self.add_plan_res(
                                    plan_res,
                                    module_res,
                                    self.create_execute_time_range(cloud_plan_start_time, cloud_plan_end_time)
                                )
                    # 计划层面的更新和写入
                    self.commit_plan_res(plan_res)
            except:
                logger.error("{} 任务执行采集 业务线 id {} 处理发生异常 {}".format(self.get_log_tag(
                    "execute"), module_id, traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} 任务执行采集 业务线 id {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), module_id, current_timestamp() - start_time))
        logger.info("{} 执行完成".format(self.get_log_tag("execute")))
