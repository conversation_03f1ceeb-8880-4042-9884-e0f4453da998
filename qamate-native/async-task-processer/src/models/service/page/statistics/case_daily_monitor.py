# -*- encoding: utf-8 -*-
"""
@Desc    :   Statistic 集成回归统计

@File    :   case_daily_monitor.py
@Time    :   2024-10-23
<AUTHOR>   xuh<PERSON><PERSON>@baidu.com
"""
import traceback
import copy
import re
from src.lib.logger import logger
from src.util.time import current_timestamp
from src.util.type import get_os_tag
from src.models.dao.db.core_regression_plan import DaoDBRegressionPlan
from src.models.dao.db.core_regression_task import DaoDBRegressionTask
from src.models.dao.db.core_regression_task_statistic import DaoDBRegressionTaskStatistic
from src.models.dao.db.core_regression_round import DaoDBRegressionRound
from src.models.dao.db.core_regression_cloud_round import DaoDBRegressionCloudRound
from src.models.dao.db.core_regression_cloud_round_detail import DaoDBRegressionCloudRoundDetail
from src.models.dao.db.case_daily_statistic import DaoDBCaseDailyStatistic
from src.models.service.data.statistic.statistic_common import ServiceDataStatisticCommon
from src.models.service.data.statistic.statistic_plan import ServiceDataStatisticPlan
from src.models.service.data.statistic.statistic_task import ServiceDataStatisticTask
from src.models.service.data.statistic.statistic_round import ServiceDataStatisticRound
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.models.dao.server.core import DaoServerCore


class ServicePageCaseDailyMonitor:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_core_regression_plan = DaoDBRegressionPlan()
        self.dao_core_regression_task = DaoDBRegressionTask()
        self.dao_core_regression_task_statistic = DaoDBRegressionTaskStatistic()
        self.dao_core_regression_round = DaoDBRegressionRound()
        self.dao_core_regression_cloud_round = DaoDBRegressionCloudRound()
        self.dao_core_regression_cloud_round_detail = DaoDBRegressionCloudRoundDetail()
        self.dao_case_daily_statistics = DaoDBCaseDailyStatistic()
        self.data_statistic_common = ServiceDataStatisticCommon()
        self.data_statistic_plan = ServiceDataStatisticPlan()
        self.data_statistic_task = ServiceDataStatisticTask()
        self.data_statistic_round = ServiceDataStatisticRound()
        self.dao_redis_lock = DaoRedisLock()
        self.dao_server_core = DaoServerCore()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.statistics.case_daily_monitor.{}]".format(method_name)

    def get_default_res(self, module_id, module_name, timestamp):
        """
        构建默认的结果
        """
        res = {
            "base": {
                "task_id": 0,  # 任务编号
                "plan_id": 0,  # 计划编号
                "last_round_id": 0,  # 最近执行的轮次编号
                "os_type": 0,  # 端类型
                "module_id": module_id,  # 业务线id
                "module_name": module_name,  # 业务线名称
                "plan_name": "",  # 计划名称
                "plan_create_time": 0,  # 测试计划创建时间
                "statistic_update_time": timestamp,  # 记录创建时间
                "total_num": 0,  # 待签章用例总数
                "auto_num": 0,  # 自动化用例总数
                "half_num": 0,  # 半自动化用例总数
                "manual_num": 0  # 手动用例总数
            },
            "sign": {
                "manual_unsign_num": 0,  # 未签章手工用例总数
                "manual_sign_num": 0,  # 已签章手工用例总数
                "half_unsign_num": 0,  # 未签章半自动化用例总数
                "half_sign_num": 0,  # 已签章半自动化用例总数
                "auto_unsign_num": 0,  # 未签章自动化用例总数
                "auto_sign_num": 0,  # 已签章自动化用例总数
                "manual_sign_pass": 0,  # 已签章手工通过总数
                "manual_sign_fail": 0,  # 已签章手工失败总数
                "half_sign_pass": 0,  # 已签章半自动通过总数
                "half_sign_fail": 0,  # 已签章半自动失败总数
                "auto_sign_pass": 0,  # 已签章自动化通过总数
                "auto_sign_fail": 0  # 已签章自动化失败总数
            },
            "execute": {
                "execute_num": 0,  # 自动化执行总数
                "execute_success_num": 0,  # 自动化执行成功总数
                "execute_failed_num": 0,  # 自动化执行失败总数
                "execute_cancel_num": 0,  # 自动化执行取消总数
                "execute_exception_num": 0,  # 自动化异常总数
                "execute_extra_num": 0  # 自动化其他状态总数
            },
            "link": {
                "manual_link_num": 0,  # 手工用例绑定自动化数目
                "auto_link_num": 0  # 自动化用例被手工用例绑定数目
            }
        }
        return res

    def get_final_round(self, round_list):
        """
        筛选轮次，返回最后一次执行的轮次 / 唯一的轮次
        """
        if len(round_list) == 0:
            return {}
        else:
            return round_list[0]

    def create_link_res(self, task_id, case_tree, case_list, execute_type, os_tag):
        """
        构建绑定结果
        """
        if execute_type == "manual":
            # 手工用例处理
            manual_link_res = self.data_statistic_round.get_link_data(
                case_tree=case_tree, case_list=case_list, execute_type="manual", os_tag=os_tag)
            task_link_info = {
                "task_id": task_id,
                "manual_link_list": []
            }
            task_link_info["manual_link_list"] = manual_link_res
            return task_link_info
        else:
            # 自动化用例处理
            auto_link_res = self.data_statistic_round.get_link_data(
                case_tree=case_tree, case_list=case_list, execute_type="auto", os_tag=os_tag)
            task_fork_info = {
                "task_id": task_id,
                "fork_node_list": []
            }
            task_fork_info["fork_node_list"] = auto_link_res
            return task_fork_info

    def get_sign_data(self, case_tree, manual_case_list, os_tag):
        """
        获取指定用例列表签章数据
        """
        sign_res = {
            "unsign_num": 0,
            "sign_num": 0,
            "pass_num": 0,
            "fail_num": 0,
            "block_num": 0,
            "skip_num": 0,
            "cancel_num": 0
        }
        if case_tree is None:
            return
        container = []
        container.append(case_tree)
        while len(container) > 0:
            node = container.pop(0)
            if len(node["children"]) != 0:
                container += node["children"]
            else:
                # 叶子结点，判断是否在手动执行的列表中
                if node["caseNodeId"] in manual_case_list:
                    # 获取签章结果
                    sign_list = node["extra"]["signInfo"][os_tag]["regressionSignList"]
                    if len(sign_list) == 0:
                        sign_res["unsign_num"] += 1
                        continue
                    else:
                        # 签章状态 1-通过 2-失败 3-阻塞 4-跳过 5-取消
                        if sign_list[0]["status"] == 4 or sign_list[0]["status"] == 5:
                            sign_res["unsign_num"] += 1
                            if sign_list[0]["status"] == 4:
                                # 跳过状态算作未签章（用以计算签章占比，不存在人力消耗）
                                sign_res["skip_num"] += 1
                        else:
                            sign_res["sign_num"] += 1
                            if sign_list[0]["status"] == 1:
                                sign_res["pass_num"] += 1
                            elif sign_list[0]["status"] == 2:
                                sign_res["fail_num"] += 1
                            elif sign_list[0]["status"] == 3:
                                sign_res["block_num"] += 1
        return sign_res

    def get_auto_data(self, round_detail, auto_case_list, os_tag):
        """
        获取该轮次的自动化数据
        """
        auto_res = {
            "execute_num": 0,
            "execute_success_num": 0,
            "execute_failed_num": 0,
            "execute_exception_num": 0,
            "execute_cancel_num": 0,
            "execute_extra_num": 0
        }
        # 扫描签章树，对于自动化用例获取type为2的签章结果
        if round_detail is None:
            return auto_res
        container = [round_detail]
        while len(container) > 0:
            node = container.pop(0)
            container += node["children"]
            if len(node["children"]) == 0:
                # 叶子结点，判断是否在手动执行的列表中
                if node["caseNodeId"] in auto_case_list:
                    # 获取签章结果
                    sign_list = node["extra"]["signInfo"][os_tag]["regressionSignList"]
                    if len(sign_list) == 0:
                        continue
                    else:
                        regressionSign = sign_list[0]
                        if regressionSign["type"] == 2:
                            if regressionSign["status"] == 1:
                                auto_res["execute_success_num"] += 1
                            elif regressionSign["status"] == 2:
                                auto_res["execute_failed_num"] += 1
                            else:
                                auto_res["execute_extra_num"] += 1
                            auto_res["execute_num"] += 1
        return auto_res

    def update_or_insert_record(self, record):
        """
        更新或插入记录
        """
        # 校验更新时间是否在本周内，存在则进行更新，不存在则进行新增
        # 查询条件 module_id, start_time, end_time
        find_res = self.dao_core_regression_task_statistic.get_record_by_task_id_and_os_type(
            task_id=record["base"]["task_id"], os_type=record["base"]["os_type"])
        if find_res is None:
            # 当前业务线不存在记录，进行插入
            self.dao_core_regression_task_statistic.insert_record(record)
        else:
            record_id = find_res["id"]
            # 当前业务线存在既有记录，进行更新
            self.dao_core_regression_task_statistic.update_record(record, record_id)

    def get_task_info(self, task_id_list, task_os_list):
        """
        构建格式化 task 列表
        """
        task_info = []
        for task_id in task_id_list:
            task_info.append({
                "task_id": task_id,
                "os_type": task_os_list[task_id],
                "round_id": "",
                "round_detail": ""
            })
        return task_info

    def get_release_module_name(self, module_name, release_frequency):
        """
        获取发布频率对应的模块名称
        """
        if release_frequency == 1:
            return module_name + "（单周）"
        elif release_frequency == 2:
            return module_name + "（双周）"
        elif release_frequency == 3:
            return module_name + "（特殊）"

    def get_manual_link_set(self, format_manual_list):
        """
        解析格式化手工绑定列表
        """
        task_manual_link_set = set()
        for link_info in format_manual_list:
            case_node_id = link_info["case_node_id"]
            link_case_node_list = link_info["link_case_node_list"]
            for link_case_node in link_case_node_list:
                task_manual_link_set.add(link_case_node)
        return task_manual_link_set

    def get_auto_fork_set(self, format_auto_list):
        """
        解析格式化自动化 fork 列表
        """
        task_auto_fork_set = set()
        for fork_info in format_auto_list:
            case_node_id = fork_info["case_node_id"]
            fork_case_node_id = fork_info["fork_case_node_id"]
            task_auto_fork_set.add(fork_case_node_id)
        return task_auto_fork_set

    def get_manual_link_num(self, link_set, manual_link_list):
        """
        为了解决一个自动化绑定多个手工，获取被有效链接的手工用例数量
        """
        count = 0
        for manual_link_info in manual_link_list:
            case_node_id = manual_link_info["case_node_id"]
            link_case_node_list = manual_link_info["link_case_node_list"]
            for link_case_node in link_case_node_list:
                if link_case_node in link_set:
                    count += 1
                    continue
        return count

    def execute_link_data(self, plan_link_info, plan_module_res):
        """
        处理绑定数据入口
        """
        # 对于 plan_link_info 进行两种类型的列表聚合
        plan_manual_link_list = plan_link_info["plan_manual_link_list"]
        plan_auto_fork_list = plan_link_info["plan_auto_fork_list"]
        if len(plan_manual_link_list) > 0:
            for manual_link in plan_manual_link_list:
                # 取出手工绑定部分
                manual_task_id = manual_link["task_id"]
                manual_link_list = manual_link["manual_link_list"]
                if len(manual_link_list) > 0:
                    # 取出自动化绑定 fork 部分
                    for auto_fork in plan_auto_fork_list:
                        auto_task_id = auto_fork["task_id"]
                        auto_fork_list = auto_fork["fork_node_list"]
                        if len(auto_fork_list) > 0:
                            # 取出两个列表的交集
                            manual_set = self.get_manual_link_set(manual_link_list)
                            auto_set = self.get_auto_fork_set(auto_fork_list)
                            intersection_set = manual_set & auto_set
                            if (len(intersection_set) > 0):
                                logger.info("{} 任务 id:{} 与 任务 id:{} 存在交集 {}".format(
                                    self.get_log_tag("execute"), manual_task_id, auto_task_id, intersection_set))
                                # 交集数目为p自动化被绑定数目，可以直接写
                                # 在这将后者 task 的自动化绑定数目写入
                                auto_module_res = plan_module_res[auto_task_id]
                                auto_module_res["link"]["auto_link_num"] += len(intersection_set)
                                # 手工数目需要返回去获取用例节点数目
                                manual_band_num = self.get_manual_link_num(intersection_set, manual_link_list)
                                # 在这将前者 task 的被自动化绑定的手工数目写入
                                manual_module_res = plan_module_res[manual_task_id]
                                manual_module_res["link"]["manual_link_num"] += manual_band_num
        return plan_module_res

    def recreate_plan_id_list(self, module_id, final_plan_id_list):
        """
        由于内核业务线集成自动化与手工用例位于不同用例组，因此需要在测试计划做聚合处理，此处为通过版本号进行重构计划 id 列表
        """
        if self.data_statistic_common.check_is_special_product(module_id=module_id) == False:
            return final_plan_id_list
        # 识别版本号聚合
        version_group_list = []
        version_set = set()
        for plan_info in final_plan_id_list:
            plan_name = plan_info["plan_name"]
            # 根据测试计划名称拆分版本号
            version_list = re.findall(r"(\d+\.\d+\.\d+)", plan_name)
            if len(version_list) > 0:
                version_tag = version_list[0]
            else:
                version_tag = "无版本号"
            if version_tag in version_set:
                new_plan_info = self.get_new_plan_info(version_group_list, version_tag)
                new_plan_info["plan_id"].append(plan_info["plan_id"])
            else:
                new_plan_info = self.create_default_conbine_plan(version_tag, plan_info)
                version_group_list.append(new_plan_info)
                version_set.add(version_tag)
        # 聚合完成，开始根据版本号进行新的计划构建
        return version_group_list

    def create_default_conbine_plan(self, version_name, plan_info):
        """
        通过版本号汇总新的测试计划列表
        """
        new_plan_info = {
            "plan_id": [],
            "plan_name": version_name,
            "plan_create_time": plan_info["plan_create_time"]
        }
        new_plan_info["plan_id"].append(plan_info["plan_id"])
        return new_plan_info

    def get_new_plan_info(self, version_group_list, version_tag):
        """
        从汇集版本信息中获取指定版本内容
        """
        if len(version_group_list) == 0:
            return None
        for version_info in version_group_list:
            if version_info["plan_name"] == version_tag:
                return version_info

    def combine_plan_id(self, module_id, plan_id):
        """
        聚合多计划 id 为一个计划 id
        """
        if self.data_statistic_common.check_is_special_product(module_id=module_id) == True:
            new_plan_id = 0
            for id in plan_id:
                new_plan_id += id
            plan_id = new_plan_id
        return plan_id

    def clean_del_record(self, del_plan_id_list):
        """
        清除已删除任务记录
        """
        # 拆解出 plan_id 列表
        del_plan_ids = []
        for plan_info in del_plan_id_list:
            del_plan_ids.append(plan_info["plan_id"])
        if len(del_plan_id_list) > 0:
            # 进行清除
            return self.dao_core_regression_task_statistic.delete_task_by_plan_id_in(plan_list=del_plan_ids)
        else:
            return 0

    def execute(self):
        """
        执行主函数
        """
        logger.info("{} 开始执行".format(self.get_log_tag("execute")))
        # 初始化日期
        date_range = self.data_statistic_common.get_default_date_range()
        # 获取该段日期内对应业务线的测试计划
        for product_info in self.data_statistic_common.get_default_product_info():
            start_time = current_timestamp()
            # 开始抢占分布式锁准备处理池子
            redis_key = "case_daily_monitor_product_{}".format(product_info["product_id"])
            is_locked = False
            module_id = product_info["product_id"]
            module_name = product_info["product_name"]
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} 统计采集 业务线 id {} 定时采集器抢占分布式锁失败".format(self.get_log_tag("execute"), module_id))
                    continue
                # 获取集成测试计划
                total_plan_list = self.data_statistic_plan.get_plan_list_by_module_id_and_time(
                    module_id=module_id, start_time=date_range["start_time"], end_time=date_range["end_time"])
                final_plan_id_list = total_plan_list["final_plan_list"]
                del_plan_id_list = total_plan_list["del_plan_list"]
                # 进行删除的计划清除
                if len(del_plan_id_list) > 0:
                    self.clean_del_record(del_plan_id_list)
                if len(final_plan_id_list) == 0:
                    logger.warning("{} 业务线 id:{} 未找到集成测试计划".format(
                        self.get_log_tag("execute"), module_id))
                    continue
                # 对于内核业务线特殊汇总测试计划
                final_plan_id_list = self.recreate_plan_id_list(module_id, final_plan_id_list)
                # 获取对应的下属 task
                for plan_info in final_plan_id_list:
                    plan_link_info = {
                        "plan_manual_link_list": [],
                        "plan_auto_fork_list": []
                    }
                    plan_module_res = {}
                    plan_id = plan_info["plan_id"]
                    module_res = self.get_default_res(module_id, module_name, date_range["end_time"])
                    if self.data_statistic_common.check_is_special_product(module_id=module_id) == False:
                        # 进行 plan_id 转列表
                        plan_list = [plan_id]
                        plan_id = plan_list
                    task_res = self.data_statistic_task.get_task_list_by_plan_id(plan_list=plan_id)
                    # 做 plan_id 的聚合
                    plan_id = self.combine_plan_id(module_id, plan_id)
                    if len(task_res) == 0:
                        logger.warning("{} 业务线 id:{} 计划 id:{} 未找到对应的任务".format(
                            module_id, self.get_log_tag("execute"), plan_id))
                        continue
                    # 对于每个 task 获取执行完的最新的任务
                    for task_info in task_res:
                        task_id = task_info["id"]
                        task_os = task_info["os_type"]
                        round_list = self.dao_core_regression_round.get_round_by_task_id(task_id=task_id)
                        os_tag = get_os_tag(os_type=task_os)
                        final_round = self.get_final_round(round_list)
                        if final_round == {}:
                            logger.warning("{} 业务线 id:{} 任务 id:{} 未找到对应的轮次".format(
                                self.get_log_tag("execute"), module_id, task_id))
                            continue
                        round_id = final_round["id"]
                        # 先统计 概览部分：总数，用例类型，占比(交给报表处理)
                        round_detail = self.data_statistic_round.get_round_detail_by_id(round_id=round_id)
                        if round_detail is None:
                            logger.warning("{} 业务线 id:{} 任务 id:{} 未找到对应的轮次详情".format(
                                self.get_log_tag("execute"), module_id, task_id))
                            continue
                        base_res = self.data_statistic_round.get_statistic_data(round_detail, os_tag)
                        # 基础信息
                        module_res["base"]["module_id"] = module_id
                        module_res["base"]["module_name"] = module_name
                        # 任务信息
                        module_res["base"]["task_id"] = task_id
                        module_res["base"]["os_type"] = task_os
                        module_res["base"]["last_round_id"] = round_id
                        module_res["base"]["plan_id"] = plan_id
                        module_res["base"]["plan_name"] = plan_info["plan_name"]
                        module_res["base"]["plan_create_time"] = plan_info["plan_create_time"]
                        # 一个轮次的用例数目
                        module_res["base"]["auto_num"] = len(base_res["auto"])
                        module_res["base"]["half_num"] = len(base_res["half"])
                        module_res["base"]["manual_num"] = len(base_res["manual"])
                        module_res["base"]["total_num"] = len(base_res["auto"])
                        module_res["base"]["total_num"] += len(base_res["half"])
                        module_res["base"]["total_num"] += len(base_res["manual"])
                        # 再统计 签章结果部分：手动用例签章占比，手动用例签章通过率，手动用例签章不通过率
                        manual_case_list = base_res["manual"]
                        manual_res = self.get_sign_data(round_detail, manual_case_list, os_tag)
                        module_res["sign"]["manual_unsign_num"] = manual_res["unsign_num"]
                        module_res["sign"]["manual_sign_num"] = manual_res["sign_num"]
                        module_res["sign"]["manual_sign_pass"] = manual_res["pass_num"]
                        module_res["sign"]["manual_sign_fail"] = manual_res["fail_num"]
                        # 半自动化签章数据
                        half_case_list = base_res["half"]
                        half_res = self.get_sign_data(round_detail, half_case_list, os_tag)
                        module_res["sign"]["half_unsign_num"] = half_res["unsign_num"]
                        module_res["sign"]["half_sign_num"] = half_res["sign_num"]
                        module_res["sign"]["half_sign_pass"] = half_res["pass_num"]
                        module_res["sign"]["half_sign_fail"] = half_res["fail_num"]
                        # 自动化签章数据
                        auto_case_list = base_res["auto"]
                        auto_res = self.get_sign_data(round_detail, auto_case_list, os_tag)
                        module_res["sign"]["auto_unsign_num"] = auto_res["unsign_num"]
                        module_res["sign"]["auto_sign_num"] = auto_res["sign_num"]
                        module_res["sign"]["auto_sign_pass"] = auto_res["pass_num"]
                        module_res["sign"]["auto_sign_fail"] = auto_res["fail_num"]
                        # 再去获取 执行自动化的: 自动化用例稳定性，自动化用例成功数，自动化用例失败数
                        auto_case_list = base_res["auto"]
                        auto_res = self.get_auto_data(round_detail, auto_case_list, os_tag)
                        module_res["execute"]["execute_num"] = auto_res["execute_num"]
                        module_res["execute"]["execute_success_num"] = auto_res["execute_success_num"]
                        module_res["execute"]["execute_failed_num"] = auto_res["execute_failed_num"]
                        module_res["execute"]["execute_cancel_num"] = auto_res["execute_cancel_num"]
                        module_res["execute"]["execute_exception_num"] = auto_res["execute_exception_num"]
                        module_res["execute"]["execute_extra_num"] = auto_res["execute_extra_num"]
                        single_module_res = copy.deepcopy(module_res)
                        plan_module_res[task_id] = single_module_res
                        # 对于签章树进行染色，解决绑定在分支节点问题
                        self.data_statistic_round.check_color_tree(
                            module_id=module_id, node=round_detail, tag='')
                        # 将绑定信息写入计划层面
                        plan_link_info["plan_manual_link_list"].append(
                            self.create_link_res(task_id, round_detail, manual_case_list, "manual", os_tag))
                        # 将 fork 信息写入计划层面
                        plan_link_info["plan_auto_fork_list"].append(
                            self.create_link_res(task_id, round_detail, auto_case_list, "auto", os_tag))
                    # 处理计划层面的绑定关系逻辑
                    plan_module_res = self.execute_link_data(plan_link_info, plan_module_res)
                    # 写入和更新数据逻辑
                    for task_id, module_res in plan_module_res.items():
                        logger.info("{} 任务 id:{} 最终结果 {}".format(self.get_log_tag("execute"),
                                                                 module_res["base"]["task_id"], module_res))
                        self.update_or_insert_record(module_res)
            except:
                logger.error("{} 业务采集 业务线 id {} 处理发生异常 {}".format(self.get_log_tag(
                    "execute"), module_id, traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} 统计采集 业务线 id {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), module_id, current_timestamp() - start_time))
        logger.info("{} 执行完成".format(self.get_log_tag("execute")))
