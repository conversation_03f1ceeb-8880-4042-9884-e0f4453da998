# -*- encoding: utf-8 -*-
"""
@Desc    :   测试计划进度流转脚本

@File    :   regression_plan_process.py
@Time    :   2024-07-30
<AUTHOR>   wang<PERSON><PERSON><EMAIL>
"""
import copy
import json
import traceback
from datetime import datetime
from src.models.service.data.regression.round import ServiceDataRegressionRound
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.dao.db.core_regression_round import DaoDBRegressionRound
from src.models.dao.db.core_regression_task import DaoDBRegressionTask
from src.models.dao.db.core_regression_plan import DaoDBRegressionPlan
from src.models.dao.db.core_regression_cloud_round import DaoDBRegressionCloudRound
from src.models.dao.db.core_regression_cloud_round_detail import DaoDBRegressionCloudRoundDetail
from src.models.dao.server.core import DaoServerCore
from src.models.dao.server.logcheck import DaoServerLogcheck
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.util.bos import BosService
from src.util.time import current_timestamp
from src.util.hi import send_hi_message
from src.util.type import get_os_tag
from src.lib.logger import logger
from src.config.env import BASE_URL


class ServicePageRegressionProcess:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.data_regression_round = ServiceDataRegressionRound()
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.dao_regression_cloud_round_detail = DaoDBRegressionCloudRoundDetail()
        self.dao_regression_cloud_round = DaoDBRegressionCloudRound()
        self.dao_regression_round = DaoDBRegressionRound()
        self.dao_regression_task = DaoDBRegressionTask()
        self.dao_regression_plan = DaoDBRegressionPlan()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_server_core = DaoServerCore()
        self.dao_server_logcheck = DaoServerLogcheck()
        self.dao_redis_lock = DaoRedisLock()
        self.bos = BosService()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.regression.regression_plan_process.{}]".format(method_name)

    def alarm_case(self, task_item, case_item, cloud_params, regression_task_status):
        """
        报警函数，用于处理报警逻辑。
        """
        try:
            round_id = task_item["round_id"]
            cloud_round_id = task_item["cloud_round_id"]
            cloud_params = json.loads(cloud_params)
            if (
                "alarmInfo" not in cloud_params
                or "toid" not in cloud_params["alarmInfo"]
                or "webhook" not in cloud_params["alarmInfo"]
                or "atuseridName" not in cloud_params["alarmInfo"]
            ):
                logger.info("{} ID {} 无需报警".format(
                    self.get_log_tag("alarm_case"), cloud_round_id))
                return None
            if "statusList" not in cloud_params["alarmInfo"]:
                cloud_params["alarmInfo"]["statusList"] = [3]
            if regression_task_status not in cloud_params["alarmInfo"]["statusList"]:
                logger.info("{} ID {} 状态 {} 不满足报警状态列表, 无需报警 {}".format(
                    self.get_log_tag("alarm_case"),
                    case_item["case_node_id"],
                    regression_task_status,
                    cloud_params["alarmInfo"]["statusList"]
                ))
                return None
            os_type = get_os_tag(os_type=cloud_params["type"])
            task_list = self.dao_core_cloud_task.get_task_by_plan_id_and_case_node_id(
                plan_id=case_item["cloud_plan_id"], case_node_id=case_item["case_node_id"])
            round_item = self.dao_regression_round.get_round_by_id(round_id=task_item["round_id"])
            plan_item = self.dao_regression_plan.get_plan_by_id(plan_id=round_item["plan_id"])
            regression_task_item = self.dao_regression_task.get_task_by_id(task_id=round_item["task_id"])
            case_dict = {}
            # 整理出所有的正式执行报告
            for task_item in task_list:
                if (
                    "retry_times" not in case_dict
                    or task_item["retry_times"] > case_dict["retry_times"]
                ):
                    case_dict = {
                        "task_info": json.loads(task_item["task_info"]),
                        "status": task_item["status"],
                        "exception_msg": task_item["exception_msg"],
                        "retry_times": task_item["retry_times"],
                        "start_time": task_item["start_time"],
                        "end_time": task_item["end_time"],
                    }
            case_result = json.loads(self.bos.read_bos(bos_link=case_dict["task_info"]["caseResult"]))
            case_name = []
            step_name = ""
            case_screenshot = ""
            for node in case_result:
                if node["nodeName"] != "before each":
                    case_name.append(node["nodeName"])
                for step in node["step"]:
                    if "result" in step:
                        if "status" in step["result"] and step["result"]["code"] != 0:
                            step_name = step["stepDesc"]
                        if "data" in step["result"] and "screenshot" in step["result"]["data"]:
                            case_screenshot = step["result"]["data"]["screenshot"]
            case_name = " - ".join(case_name)
            if step_name != "":
                case_name = "{} # {}".format(case_name, step_name)
            href = ""
            if 4 == plan_item["plan_type"]:
                href = "{}/#/plan/daily/detail?caseNodeId={}&moduleId={}&planId={}&status={}&taskId={}&roundId={}&treeNodeId={}".format(
                    BASE_URL,
                    case_item["case_node_id"],
                    plan_item["module_id"],
                    round_item["plan_id"],
                    os_type,
                    round_item["task_id"],
                    round_id,
                    regression_task_item["tree_node_id"]
                )
            else:
                href = "{}/#/plan/detail?caseNodeId={}&moduleId={}&planId={}&status={}&taskId={}&roundId={}&treeNodeId={}".format(
                    BASE_URL,
                    case_item["case_node_id"],
                    plan_item["module_id"],
                    round_item["plan_id"],
                    os_type,
                    round_item["task_id"],
                    round_id,
                    regression_task_item["tree_node_id"]
                )
            message_body = [
                {
                    "content": "===== QAMate 报警 | {} =====\n".format(
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    "type": "TEXT"
                },
                {
                    "content": "【用例名称】{}\n".format(case_name),
                    "type": "TEXT"
                },
                {
                    "content": "【测试报告】",
                    "type": "TEXT"
                },
                {
                    "href": href,
                    "type": "LINK"
                },
                {
                    "content": "\n",
                    "type": "TEXT"
                },
                {
                    "content": "【报警原因】{}\n".format(case_dict["exception_msg"]),
                    "type": "TEXT"
                }
            ]
            if case_screenshot != "":
                message_body.append({
                    "content": "【执行截图】",
                    "type": "TEXT"
                })
                message_body.append({
                    "href": case_screenshot,
                    "type": "LINK"
                })
                message_body.append({
                    "content": "\n",
                    "type": "TEXT"
                })
            if len(cloud_params["alarmInfo"]["atuseridName"]) > 0:
                message_body.append({
                    "atuserids": cloud_params["alarmInfo"]["atuseridName"],
                    "atall": False,
                    "type": "AT"
                })
                message_body.append({
                    "content": " 请相关同学关注上述报警\n",
                    "type": "TEXT"
                })
            send_hi_message(
                webhook=cloud_params["alarmInfo"]["webhook"],
                params=json.dumps({
                    "message": {
                        "header": {
                            "toid": [int(cloud_params["alarmInfo"]["toid"])]
                        },
                        "body": message_body
                    }
                }, ensure_ascii=False)
            )
        except:
            logger.warning("{} ID {} 报警失败 {}".format(
                self.get_log_tag("alarm_case"), cloud_round_id, traceback.format_exc().replace('\n', ' ')))

    def get_logcheck_task(self, cloud_round_id, cloud_plan_id, cloud_params):
        """
        处理任务是否需要点位校验
        """
        try:
            cloud_params = json.loads(cloud_params)
            if (
                "logcheckInfo" not in cloud_params
                or "needLogCheck" not in cloud_params["logcheckInfo"]
                or "cuid" not in cloud_params["logcheckInfo"]
                or False == cloud_params["logcheckInfo"]["needLogCheck"]
            ):
                logger.info("{} ID {} 无需点位报告".format(
                    self.get_log_tag("get_logcheck_task"), cloud_round_id))
                return None
            cuid = cloud_params["logcheckInfo"]["cuid"]
            task_list = self.dao_core_cloud_task.get_task_by_plan_id(plan_id=cloud_plan_id)
            case_dict = {}
            # 整理出所有的正式执行报告
            for task_item in task_list:
                if 0 != task_item["type"]:
                    continue
                if (
                    task_item["case_node_id"] not in case_dict
                    or task_item["retry_times"] > case_dict[task_item["case_node_id"]]["retry_times"]
                ):
                    case_dict[task_item["case_node_id"]] = {
                        "task_info": json.loads(task_item["task_info"]),
                        "status": task_item["status"],
                        "retry_times": task_item["retry_times"],
                        "start_time": task_item["start_time"],
                        "end_time": task_item["end_time"],
                    }
            logcheck_list = []
            # 单 case 处理结果
            for case_node_id in case_dict.keys():
                # 仅处理成功执行的
                if case_dict[case_node_id]["status"] not in [4, 5]:
                    continue
                case_result = json.loads(self.bos.read_bos(
                    bos_link=case_dict[case_node_id]["task_info"]["caseResult"]))
                case_name = []
                ubc_list = []
                for node in case_result:
                    case_name.append(node["nodeName"])
                    for step in node["step"]:
                        if (
                            502 != step["stepType"]
                            or "result" not in step
                            or "data" not in step["result"]
                            or "extra" not in step["result"]["data"]
                            or "logCheckInfo" not in step["result"]["data"]["extra"]
                            or "ruleId" not in step["result"]["data"]["extra"]["logCheckInfo"]
                        ):
                            continue
                        ubc_list.append(str(step["result"]["data"]["extra"]["logCheckInfo"]["ruleId"]))
                if 0 == len(ubc_list):
                    continue
                check_item = {
                    "case_id": case_node_id,
                    "case_name": "#".join(case_name),
                    "start_time": case_dict[case_node_id]["start_time"],
                    "end_time": case_dict[case_node_id]["end_time"],
                    "task_status": 0,
                    "cuid": cuid,
                    "scenes_ids": ",".join(ubc_list)
                }
                if 5 == case_dict[case_node_id]["status"]:
                    check_item["task_status"] = -1
                logcheck_list.append(copy.deepcopy(check_item))
            # 未获取到需要的打点信息
            if 0 == len(logcheck_list):
                logger.info("{} ID {} 未获取到需要上报的打点信息".format(
                    self.get_log_tag("get_logcheck_task"), cloud_round_id))
                return None
            # 组拼请求信息上报
            report_link = self.dao_server_logcheck.get_logcheck_task_by_params(params={
                "lazyone_plan_id": cloud_plan_id,
                "description": "QAMate 3.0 测试计划自动化小助手",
                "creater": "QAMate",
                "cases": logcheck_list
            })
            if "" == report_link:
                logger.info("{} ID {} 未获取到打点任务报告".format(
                    self.get_log_tag("get_logcheck_task"), cloud_round_id))
                return None
            self.dao_regression_cloud_round.update_cloud_round_extra(
                id=cloud_round_id,
                extra={
                    "logcheckInfo": {
                        "reportLink": report_link
                    }
                }
            )
        except:
            logger.warning("{} ID {} 获取打点平台任务异常 {}".format(
                self.get_log_tag("get_logcheck_task"), cloud_round_id, traceback.format_exc().replace('\n', ' ')))

    def execute(self):
        """
        执行主函数
        """
        redis_key = None
        unend_task_list = self.dao_regression_cloud_round.get_unend_cloud_plan()
        end_round_list = self.data_regression_round.get_end_round_list(
            round_list=list(set([item["round_id"] for item in unend_task_list])))
        un_pending_cloud_task_list = self.data_cloud_plan.get_un_pending_plan_by_id_list(
            plan_list=list(set([item["cloud_plan_id"] for item in unend_task_list])))
        for task_item in unend_task_list:
            if (
                0 == task_item["status"]
                and task_item["cloud_plan_id"] not in un_pending_cloud_task_list
                and task_item["round_id"] not in end_round_list
            ):
                logger.info("{} ID {} 对应的 cloud 计划尚未开始且轮次未结束，跳过".format(
                    self.get_log_tag("execute"), task_item["cloud_round_id"]))
                continue
            start_time = current_timestamp()
            logger.info("{} 准备处理 ID {}".format(self.get_log_tag("execute"), task_item["cloud_round_id"]))
            redis_key = "regression_plan_process_{}".format(task_item["cloud_round_id"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60 * 10):
                    logger.warning("{} ID {} 获取分布式锁失败".format(self.get_log_tag("execute"),
                                                              task_item["cloud_round_id"]))
                    continue
                is_locked = True
                # 如果轮次已结束, 则直接结束该自动化轮次
                if task_item["round_id"] in end_round_list:
                    logger.info("{} ID {} 对应的轮次已结束, 直接结束该自动化轮次".format(
                        self.get_log_tag("execute"), task_item["cloud_round_id"]))
                    self.dao_regression_cloud_round.cancel_cloud_round(
                        id=task_item["cloud_round_id"],
                        msg="轮次结束任务跟随取消"
                    )
                    self.dao_core_cloud_plan.cancel_plan(
                        plan_id=task_item["cloud_plan_id"],
                        exception_msg="轮次结束任务跟随取消"
                    )
                # 如果任务正在排队中去检索下任务是否开始
                else:
                    cloud_plan_item = self.dao_core_cloud_plan.get_plan_by_id_list(
                        id_list=[task_item["cloud_plan_id"]])[0]
                    # 处理任务执行状态，条件满足：
                    # 1. 任务状态为未完成态
                    # 2. 云端计划状态不在初始化 & 排队状态中
                    if task_item["status"] in [0, 1] and cloud_plan_item["status"] not in [0, 1]:
                        task_list = self.dao_core_cloud_task.get_task_by_plan_id(plan_id=task_item["cloud_plan_id"])
                        unend_case_list = self.dao_regression_cloud_round_detail.get_unend_cloud_task(
                            round_id=task_item["round_id"], cloud_plan_id=task_item["cloud_plan_id"])
                        # 按用例结果去处理任务状态
                        for case_item in unend_case_list:
                            real_task_status = None
                            real_task_try_times = None
                            real_task_msg = None
                            real_task_info = None
                            # 找到执行任务中最后一次重试任务
                            for cloud_task_item in task_list:
                                if (
                                    cloud_task_item["case_node_id"] == case_item["case_node_id"]
                                    and (
                                        real_task_try_times is None
                                        or cloud_task_item["retry_times"] > real_task_try_times
                                    )
                                ):
                                    real_task_try_times = cloud_task_item["retry_times"]
                                    real_task_status = cloud_task_item["status"]
                                    real_task_msg = cloud_task_item["exception_msg"]
                                    real_task_info = cloud_task_item["task_info"]
                            # 如果没找到就直接跳过
                            if real_task_status is None:
                                logger.warning("{} ID {} caseNodeId {} 未找到执行任务".format(
                                    self.get_log_tag("execute"),
                                    task_item["cloud_round_id"],
                                    case_item["case_node_id"]
                                ))
                                self.dao_regression_cloud_round_detail.except_cloud_task(
                                    id=case_item["cloud_detail_id"], msg="未找到执行任务")
                                continue
                            # 任务状态处理
                            if real_task_status in [2, 3] and 0 == case_item["status"]:
                                logger.info("{} ID {} caseNodeId {} 任务开始执行".format(
                                    self.get_log_tag("execute"),
                                    task_item["cloud_round_id"],
                                    case_item["case_node_id"]
                                ))
                                self.dao_regression_cloud_round_detail.start_cloud_task(id=case_item["cloud_detail_id"])
                            # 任务执行通过
                            elif 4 == real_task_status:
                                task_info = json.loads(real_task_info)
                                if "isPendingManualCase" in task_info and True == task_info["isPendingManualCase"]:
                                    logger.info("{} ID {} caseNodeId {} 任务执行待复验".format(
                                        self.get_log_tag("execute"),
                                        task_item["cloud_round_id"],
                                        case_item["case_node_id"]
                                    ))
                                    self.dao_regression_cloud_round_detail.manual_cloud_task(
                                        id=case_item["cloud_detail_id"], msg=real_task_msg)
                                    self.dao_server_core.sign_case(
                                        case_node_id_list=[case_item["case_node_id"]],
                                        os_type=case_item["os_type"],
                                        status=5,
                                        plan_id=task_item["cloud_plan_id"],
                                        create_user=task_item["create_user"]
                                    )
                                else:
                                    logger.info("{} ID {} caseNodeId {} 任务执行通过".format(
                                        self.get_log_tag("execute"),
                                        task_item["cloud_round_id"],
                                        case_item["case_node_id"]
                                    ))
                                    self.dao_server_core.sign_case(
                                        case_node_id_list=[case_item["case_node_id"]],
                                        os_type=case_item["os_type"],
                                        status=1,
                                        plan_id=task_item["cloud_plan_id"],
                                        create_user=task_item["create_user"]
                                    )
                                    self.dao_regression_cloud_round_detail.success_cloud_task(
                                        id=case_item["cloud_detail_id"], msg=real_task_msg)
                                    self.alarm_case(task_item, case_item, task_item["cloud_params"], 2)
                            # 任务执行不通过
                            elif 5 == real_task_status:
                                logger.info("{} ID {} caseNodeId {} 任务执行不通过".format(
                                    self.get_log_tag("execute"),
                                    task_item["cloud_round_id"],
                                    case_item["case_node_id"]
                                ))
                                self.dao_server_core.sign_case(
                                    case_node_id_list=[case_item["case_node_id"]],
                                    os_type=case_item["os_type"],
                                    status=2,
                                    plan_id=task_item["cloud_plan_id"],
                                    create_user=task_item["create_user"]
                                )
                                self.dao_regression_cloud_round_detail.fail_cloud_task(
                                    id=case_item["cloud_detail_id"], msg=real_task_msg)
                                self.alarm_case(task_item, case_item, task_item["cloud_params"], 3)
                            # 任务执行异常
                            elif 6 == real_task_status:
                                logger.info("{} ID {} caseNodeId {} 任务执行异常".format(
                                    self.get_log_tag("execute"),
                                    task_item["cloud_round_id"],
                                    case_item["case_node_id"]
                                ))
                                self.dao_regression_cloud_round_detail.except_cloud_task(
                                    id=case_item["cloud_detail_id"], msg=real_task_msg)
                                self.alarm_case(task_item, case_item, task_item["cloud_params"], 4)
                            else:
                                logger.info("{} ID {} caseNodeId {} 任务状态不变, 无需处理".format(
                                    self.get_log_tag("execute"),
                                    task_item["cloud_round_id"],
                                    case_item["case_node_id"]
                                ))

                    # 处理计划轮询状态
                    if cloud_plan_item["status"] in [0, 1] and 0 == task_item["status"]:
                        logger.info("{} ID {} 对应的轮次自动化计划状态不变, 无需处理".format(
                            self.get_log_tag("execute"), task_item["cloud_round_id"]))
                    elif 2 == cloud_plan_item["status"] and 0 == task_item["status"]:
                        logger.info("{} ID {} 对应的轮次自动化计划状态已开始, 更新任务状态".format(
                            self.get_log_tag("execute"), task_item["cloud_round_id"]))
                        self.dao_regression_cloud_round.start_cloud_round(id=task_item["cloud_round_id"])
                    elif 3 == cloud_plan_item["status"]:
                        logger.info("{} ID {} 对应的轮次自动化计划状态已结束, 更新任务状态".format(
                            self.get_log_tag("execute"), task_item["cloud_round_id"]))
                        self.dao_regression_cloud_round.end_cloud_round(id=task_item["cloud_round_id"])
                        self.get_logcheck_task(
                            cloud_round_id=task_item["cloud_round_id"],
                            cloud_plan_id=task_item["cloud_plan_id"],
                            cloud_params=task_item["cloud_params"]
                        )
                    elif 4 == cloud_plan_item["status"]:
                        logger.info("{} ID {} 对应的轮次自动化计划状态异常, 更新任务状态".format(
                            self.get_log_tag("execute"), task_item["cloud_round_id"]))
                        self.dao_regression_cloud_round.except_cloud_round(
                            id=task_item["cloud_round_id"], msg=cloud_plan_item["exception_msg"])
                        self.dao_regression_cloud_round_detail.except_running_cloud_round_task(
                            id=task_item["cloud_plan_id"])
                        self.get_logcheck_task(
                            cloud_round_id=task_item["cloud_round_id"],
                            cloud_plan_id=task_item["cloud_plan_id"],
                            cloud_params=task_item["cloud_params"]
                        )
                    elif 5 == cloud_plan_item["status"]:
                        logger.info("{} ID {} 对应的轮次自动化计划状态已被取消, 更新任务状态".format(
                            self.get_log_tag("execute"), task_item["cloud_round_id"]))
                        self.dao_regression_cloud_round.cancel_cloud_round(
                            id=task_item["cloud_round_id"],
                            msg="自动化计划取消跟随取消"
                        )
                        self.dao_regression_cloud_round_detail.except_running_cloud_round_task(
                            id=task_item["cloud_plan_id"])
                        self.get_logcheck_task(
                            cloud_round_id=task_item["cloud_round_id"],
                            cloud_plan_id=task_item["cloud_plan_id"],
                            cloud_params=task_item["cloud_params"]
                        )
                    else:
                        logger.info("{} ID {} 对应的轮次自动化计划状态不变, 无需处理".format(
                            self.get_log_tag("execute"), task_item["cloud_round_id"]))
            except:
                logger.error("{} ID {} 处理异常 {}".format(self.get_log_tag(
                    "execute"), task_item["cloud_round_id"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                # 释放分布式锁
                logger.info("{} ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), task_item["cloud_round_id"], current_timestamp()-start_time))
