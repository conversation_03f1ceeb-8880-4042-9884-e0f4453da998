# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 任务后置处理脚本实际处理逻辑控制

@File    :   cloud_task_result_process.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import json
import traceback
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.models.dao.server.core import DaoServerCore
from src.util.time import current_timestamp
from src.lib.logger import logger


class ServicePageCloudTaskResultProcess:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_server_core = DaoServerCore()
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.dao_redis_lock = DaoRedisLock()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.cloud.cloud_task_result_process.{}]".format(method_name)

    def get_task_real_status(self, code):
        """
        根据 code 判断任务状态
        """
        if 0 == code:
            return 4
        if 0 == (code // 100) % 10:
            return 6
        else:
            return 5

    def handle_normal_task(self, task):
        """
        单处理一个 Case 任务
        """
        real_status = self.get_task_real_status(task["code"])
        # 处理是否需要任务重试
        need_retry = False
        # 当任务未达到最大重试次数时, 且计划状态为执行中时, 才需要进行重试
        if 4 != real_status:
            logger.info("{} 任务ID {} 需要重试".format(self.get_log_tag("handle_normal_task"), task["task_id"]))
            plan = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=[task["plan_id"]])[0]
            plan_params = json.loads(plan["plan_params"])
            max_retry_times = 0
            if "retryTimes" in plan_params:
                max_retry_times = plan_params["retryTimes"]
            # 两种情况需要重试:
            # 1. 任务暂时未到最大执行次数
            # 2. 任务已经是最后一次重试，但是任务异常了，则补充重试一次
            # 注意: 这里会有两个前提。1. 计划状态为执行中 2. 业务配置了重试开启
            if (
                2 == plan["status"] and (
                    task["retry_times"] < max_retry_times
                    or (max_retry_times == task["retry_times"] and 6 == real_status and 0 != max_retry_times)
                )
            ):
                need_retry = True
                new_task_info = json.loads(task["task_info"])
                if "caseResult" in new_task_info:
                    del new_task_info["caseResult"]
                self.dao_core_cloud_task.insert_task(
                    plan_id=task["plan_id"],
                    case_id=task["case_node_id"],
                    task_info=json.dumps(new_task_info, ensure_ascii=False),
                    type=0,
                    retry_times=task["retry_times"] + 1
                )
        # 处理当前任务状态
        self.dao_core_cloud_task.update_status_by_id(task_id=task["task_id"], new_status=real_status)
        logger.info("{} 任务ID {} 状态更新完成".format(self.get_log_tag("handle_normal_task"), task["task_id"]))
        # 处理是否需要结束计划
        # 不需要重试则代表该用例彻底结束
        if not need_retry:
            # 判断是否需要结束计划
            type_dict = self.dao_core_cloud_task.get_unrun_count_by_plan_id(plan_id=task["plan_id"])
            # 所有任务都执行结束且没有后置任务也全部结束
            if 0 == type_dict["0"] and 0 == type_dict["3"]:
                self.dao_core_cloud_plan.end_plan(plan_id=task["plan_id"])
                logger.info("{} 任务ID {} 完成后计划也全部结束".format(self.get_log_tag("handle_normal_task"), task["task_id"]))

    def handle_after_task(self, task):
        """
        处理后置任务
        """
        # 判断是否需要结束计划
        type_dict = self.dao_core_cloud_task.get_unrun_count_by_plan_id(plan_id=task["plan_id"])
        # 有任务没有处理结束则先处理其他任务
        if 0 != type_dict["0"]:
            return
        self.dao_core_cloud_plan.end_plan(plan_id=task["plan_id"])
        logger.info("{} 任务ID {} 完成后计划也全部结束".format(self.get_log_tag("handle_after_task"), task["task_id"]))
        self.dao_core_cloud_task.update_status_by_id(task_id=task["task_id"], new_status=4)
        logger.info("{} 任务ID {} 状态更新完成".format(self.get_log_tag("handle_after_task"), task["task_id"]))

    def execute(self):
        """
        执行主函数
        """
        task_list = self.dao_core_cloud_task.get_task_by_status(status=3, limit=10)
        task_list += self.dao_core_cloud_task.get_after_task_by_status(status=3, limit=10)
        for task in task_list:
            start_time = current_timestamp()
            redis_key = "cloud_task_result_process_{}".format(task["task_id"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} 任务ID {} 抢占分布式锁失败".format(self.get_log_tag("execute"), task["task_id"]))
                    continue
                is_locked = True
                if 3 == self.dao_core_cloud_task.get_task_by_id(task_id=task["task_id"])["status"]:
                    if 0 == task["type"]:
                        self.handle_normal_task(task)
                    elif 3 == task["type"]:
                        self.handle_after_task(task)
                else:
                    logger.info("{} 任务ID {} 已被其他实例处理过".format(self.get_log_tag("execute"), task["task_id"]))
                logger.info("{} 任务ID {} 结束处理".format(self.get_log_tag("execute"), task["task_id"]))
            except:
                logger.error("{} 任务ID {} 处理出现异常 {}".format(self.get_log_tag(
                    "execute"), task["task_id"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} 任务ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), task["task_id"], current_timestamp()-start_time))
