# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 计划拆分脚本实际处理逻辑控制

@File    :   cloud_plan_execute.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import json
import traceback
from concurrent.futures import ThreadPoolExecutor
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.service.data.device.device import ServiceDataDevice
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.util.time import current_timestamp
from src.lib.logger import logger

MAX_INT32 = 2147483647


class ServicePageCloudPlanExecute:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.data_device = ServiceDataDevice()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_redis_lock = DaoRedisLock()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.cloud.cloud_plan_execute.{}]".format(method_name)

    def start_plan(self, pool_id, plan_id):
        """
        开始执行计划
        """
        if False == self.data_device.occupy_pool(pool_id=pool_id, plan_id=plan_id):
            return False
        self.data_cloud_plan.start_plan(plan_id=plan_id)

    def renew_pool(self, pool_id):
        """
        重新分配设备池
        """
        pool_list = self.data_device.get_ui_pool_list()
        for pool in pool_list:
            if pool["poolId"] == pool_id:
                return pool

    def handle_one_pool(self, pool):
        start_time = current_timestamp()
        logger.info("{} 准备处理 ID {} 设备池".format(self.get_log_tag("handle_one_pool"), pool["poolId"]))
        redis_key = "cloud_plan_execute_pool_{}".format(pool["poolId"])
        is_locked = False
        try:
            if False == self.dao_redis_lock.lock(redis_key, 60):
                logger.warning("{} ID {} 设备池抢占分布式锁失败".format(self.get_log_tag("handle_one_pool"), pool["poolId"]))
                return
            is_locked = True
            logger.info("{} ID {} 设备池开始处理".format(self.get_log_tag("handle_one_pool"), pool["poolId"]))
            pool = self.renew_pool(pool["poolId"])
            # 本地设备池不设最大 QPS，云端设备池则为池内设备总数
            max_qps = MAX_INT32
            alive_device_set = set()
            if 1 == pool["taskType"]:
                pool_device_list = self.data_device.get_pool_device_list(
                    pool_id=pool["poolId"], module_id=pool["moduleId"])
                for device in pool_device_list:
                    if (2 == device["status"] or 3 == device["status"]) and 0 == device["operateStatus"]:
                        alive_device_set.add(device["deviceId"])
                # 池里数量 与 1.5 倍在线数量取最小值，防止 API 呢边堆积过多无效设备，导致无效计划执行堆积
                max_qps = int(min(1.5 * len(alive_device_set), len(pool_device_list)))
            # 获取正在执行的计划
            execute_plan_list = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=pool["occupyPlanList"])
            for execute_plan in execute_plan_list:
                if 2 != execute_plan["status"]:
                    try:
                        self.data_device.release_pool(pool_id=pool["poolId"], plan_id=execute_plan["id"])
                        logger.info("{} 计划ID {} 处于终态释放设备池ID {}".format(
                            self.get_log_tag("handle_one_pool"), execute_plan["id"], pool["poolId"]))
                    except:
                        pass
            # 计算执行计划用到了哪些设备以及是否独享池
            is_exclusive = False
            occupy_device_set = set()
            occupy_qps_sum = 0
            for execute_plan in execute_plan_list:
                # 如果有正在执行的独享计划就直接结束不用管了
                if True == is_exclusive:
                    break
                plan_params = json.loads(execute_plan["plan_params"])
                if self.data_cloud_plan.is_plan_exclusive(plan_params=plan_params):
                    is_exclusive = True
                elif (
                    "deviceIdList" in plan_params["planParams"]
                    and len(plan_params["planParams"]["deviceIdList"]) > 0
                ):
                    occupy_device_set = occupy_device_set | set(plan_params["planParams"]["deviceIdList"])
                elif "qps" in plan_params["planParams"] and int(plan_params["planParams"]["qps"]) > 0:
                    occupy_qps_sum += int(plan_params["planParams"]["qps"])
            logger.info("{} ID {} 设备池处理当前执行状态完毕 指定占用设备有 {} 指定 QPS 有 {}".format(
                self.get_log_tag("handle_one_pool"), pool["poolId"], list(occupy_device_set), occupy_qps_sum))
            # 在没有独享设备的情况下才进行之后的任务分配
            # 在没有达到 QPS 上限的时候才进行任务分配
            index = 0
            while False == is_exclusive and occupy_qps_sum + len(occupy_device_set & alive_device_set) < max_qps:
                # 获取正在这个池后面排队的计划
                pending_plan_list = self.dao_core_cloud_plan.get_plan_by_status_and_pool(
                    status=1, pool_id=pool["poolId"], index=index, limit=10
                )
                # 没任务处理了则直接结束
                if 0 == len(pending_plan_list):
                    break
                # 轮询等待中的计划
                for pending_plan in pending_plan_list:
                    logger.info("{} ID {} 设备池尝试匹配 ID {} 计划".format(
                        self.get_log_tag("handle_one_pool"), pool["poolId"], pending_plan["id"]))
                    plan_params = json.loads(pending_plan["plan_params"])
                    # 判断该任务是否独享设备池
                    if self.data_cloud_plan.is_plan_exclusive(plan_params=plan_params):
                        logger.info("{} ID {} 设备池匹配 ID {} 计划成功并独享".format(
                            self.get_log_tag("handle_one_pool"), pool["poolId"], pending_plan["id"]))
                        self.start_plan(pool["poolId"], pending_plan["id"])
                        is_exclusive = True
                    # 判断设备是否有空闲指定的设备
                    elif (
                        "deviceIdList" in plan_params["planParams"]
                        and len(plan_params["planParams"]["deviceIdList"]) > 0
                    ):
                        # 找出是否存在没有被前面计划占用的设备
                        device_set = set(plan_params["planParams"]["deviceIdList"]) - occupy_device_set
                        # 有设备空余可占用 压入执行队列
                        if len(device_set) > 0:
                            logger.info("{} ID {} 设备池通过指定设备匹配 ID {} 计划成功但不独享".format(
                                self.get_log_tag("handle_one_pool"), pool["poolId"], pending_plan["id"]))
                            self.start_plan(pool["poolId"], pending_plan["id"])
                            occupy_device_set = occupy_device_set | set(
                                plan_params["planParams"]["deviceIdList"])
                        else:
                            logger.info("{} ID {} 设备池通过指定设备匹配 ID {} 计划失败, 没有空闲设备".format(
                                self.get_log_tag("handle_one_pool"), pool["poolId"], pending_plan["id"]))
                    # 判断设备是否有空闲的 QPS 供使用（不需要判断 QPS 是否够，只要能进场就行，QPS 控制由任务分配来决定）
                    elif "qps" in plan_params["planParams"] and int(plan_params["planParams"]["qps"]) > 0:
                        logger.info("{} ID {} 设备池通过 QPS 匹配 ID {} 计划成功但不独享".format(
                            self.get_log_tag("handle_one_pool"), pool["poolId"], pending_plan["id"]))
                        self.start_plan(pool["poolId"], pending_plan["id"])
                        occupy_qps_sum += int(plan_params["planParams"]["qps"])
                    # 如果已经有独享设备或者已经达到 QPS 上限则直接结束
                    if True == is_exclusive or occupy_qps_sum + len(occupy_device_set & alive_device_set) >= max_qps:
                        break
                index += 1
            else:
                logger.info("{} ID {} 设备池被独享, 跳过".format(self.get_log_tag("handle_one_pool"), pool["poolId"]))
            logger.info("{} ID {} 设备池结束处理".format(self.get_log_tag("handle_one_pool"), pool["poolId"]))
        except:
            logger.error("{} ID {} 设备池处理异常 {}".format(self.get_log_tag(
                "execute"), pool["poolId"], traceback.format_exc().replace('\n', ' ')))
        finally:
            if is_locked:
                self.dao_redis_lock.unlock(redis_key)
            logger.info("{} ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                "execute"), pool["poolId"], current_timestamp()-start_time))

    def execute(self):
        """
        执行主函数
        """
        pool_list = self.data_device.get_ui_pool_list()
        with ThreadPoolExecutor(max_workers=5) as executor:
            # 提交所有任务
            futures = [executor.submit(self.handle_one_pool, pool) for pool in pool_list]
            # 等待所有任务完成
            for future in futures:
                future.result()
