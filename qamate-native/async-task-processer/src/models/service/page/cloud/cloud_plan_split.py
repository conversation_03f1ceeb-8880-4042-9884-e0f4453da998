# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 计划拆分脚本实际处理逻辑控制

@File    :   cloud_plan_split.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import copy
import json
import traceback
import random
from src.models.service.data.cloud.plan_split.qamate_3_0 import ServiceDataCloudPlanSplitQAMate3
from src.models.service.data.cloud.plan_split.generate import ServiceDataCloudPlanSplitGenerate
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.util.time import current_timestamp
from src.lib.logger import logger


class ServicePageCloudPlanSplit:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_redis_lock = DaoRedisLock()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.cloud.cloud_plan_split.{}]".format(method_name)

    def execute(self):
        """
        执行主函数
        """
        logger.info("{} 开始新的一轮拆分执行".format(self.get_log_tag("execute")))
        # 找到每个 Module 下需要拆分的计划 ( 状态为待处理, 且可以抢占到分布式锁, 代表没有别的进程在处理该计划 )
        plan_list = self.dao_core_cloud_plan.get_plan_by_status_and_module(status=0, limit=3)
        if 0 == len(plan_list):
            logger.info("{} 没有可用计划, 结束本次执行".format(self.get_log_tag("execute")))
            return
        plan_dict = {}
        for plan_item in plan_list:
            if str(plan_item["module_id"]) not in plan_dict:
                plan_dict[str(plan_item["module_id"])] = [plan_item]
            else:
                plan_dict[str(plan_item["module_id"])].append(plan_item)
        # 根据所有 Module 挨个处理任务
        for module_id in plan_dict.keys():
            redis_key = None
            plan_item = None
            plan_list = plan_dict[str(module_id)]
            logger.info("{} 获取到 Module {} 待初始化的计划 {} 个".format(self.get_log_tag("execute"), module_id,
                                                               len(plan_list)))
            # 取用随机数抗干扰
            for i in range(10):
                item = plan_list[random.randint(0, len(plan_list) - 1)]
                redis_key = "cloud_plan_split_{}".format(item["plan_id"])
                if False == self.dao_redis_lock.lock(redis_key, 60 * 10):
                    logger.warning("{} ID {} 获取分布式锁失败".format(self.get_log_tag("execute"), item["plan_id"]))
                    continue
                plan_item = copy.deepcopy(item)
                break
            if plan_item is None:
                return
            # 实际处理该计划
            start_time = current_timestamp()
            try:
                logger.info("{} ID {} 开始处理".format(self.get_log_tag("execute"), plan_item["plan_id"]))
                cloud_plan_item = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=[plan_item["plan_id"]])[0]
                if 0 == cloud_plan_item["status"]:
                    self.dao_core_cloud_task.delete_task_by_plan(plan_id=plan_item["plan_id"])
                    logger.info("{} ID {} 删除原有执行任务".format(self.get_log_tag("execute"), plan_item["plan_id"]))
                    # 拆分计划任务
                    plan_info, create_res = None, None
                    if item["plan_type"] in [1, 2]:
                        logger.info("{} ID {} 属于 QAMATE 3.0 计划".format(
                            self.get_log_tag("execute"), plan_item["plan_id"]))
                        plan_info, create_res = ServiceDataCloudPlanSplitQAMate3(plan_item=plan_item).handler_3_0_plan()
                    elif 5 == item["plan_type"]:
                        logger.info(
                            "{} ID {} 属于 UI 生成 计划".format(self.get_log_tag("execute"), plan_item["plan_id"]))
                        plan_info, create_res = ServiceDataCloudPlanSplitGenerate(plan_item=plan_item).handler_plan()
                    elif item["plan_type"] in [6, 7]:
                        logger.info(
                            "{} ID {} 属于 MONKEY 计划".format(self.get_log_tag("execute"), plan_item["plan_id"]))
                        plan_info, create_res = ServiceDataCloudPlanSplitQAMate3(plan_item=plan_item).handler_3_0_plan()
                    elif item["plan_type"] in [8, 9]:
                        logger.info(
                            "{} ID {} 属于速度评测计划".format(self.get_log_tag("execute"), plan_item["plan_id"]))
                        plan_info, create_res = ServiceDataCloudPlanSplitQAMate3(plan_item=plan_item).handler_3_0_plan()
                    # 更新计划状态
                    if True == create_res["success"]:
                        logger.info("{} ID {} 完成执行任务拆分准备更新计划状态".format(
                            self.get_log_tag("execute"), plan_item["plan_id"]))
                        self.data_cloud_plan.ready_plan(
                            plan_id=plan_item["plan_id"], plan_info=json.dumps(plan_info, ensure_ascii=False))
                    else:
                        logger.info("{} ID {} 拆分异常, 直接更新计划状态".format(self.get_log_tag("execute"),
                                                                     plan_item["plan_id"]))
                        self.data_cloud_plan.except_plan(plan_id=plan_item["plan_id"], exception_msg=create_res["msg"])
                else:
                    logger.warning(
                        "{} ID {} 状态已过期，不进行处理".format(self.get_log_tag("execute"), plan_item["plan_id"]))
            except:
                logger.error("{} ID {} 处理异常 {}".format(self.get_log_tag(
                    "execute"), plan_item["plan_id"], traceback.format_exc().replace('\n', ' ')))
            finally:
                # 释放分布式锁
                logger.info("{} ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), plan_item["plan_id"], current_timestamp() - start_time))
                self.dao_redis_lock.unlock(redis_key)
