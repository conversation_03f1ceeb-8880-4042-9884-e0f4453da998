# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 任务分配监控脚本实际处理逻辑控制

@File    :   cloud_task_assign_monitor.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import json
import traceback
from models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_assign_task import Dao<PERSON>BCoreAssignTask
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.dao.server.lazydevice_v2 import DaoServerLazydeviceV2
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.util.time import current_timestamp
from src.lib.logger import logger


class ServicePageCloudTaskAssignMonitor:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.dao_core_assign_task = DaoDBCoreAssignTask()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_device_v2 = DaoServerLazydeviceV2()
        self.dao_redis_lock = DaoRedisLock()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.cloud.cloud_task_assign_monitor.{}]".format(method_name)

    def execute(self):
        """
        执行主函数
        """
        # BIG HINT！！！
        # 超过 60 秒没有拉取的任务。完成逻辑退回
        # END
        # 获取超时任务列表
        assign_task_list = self.dao_core_assign_task.get_expire_assign(expire_seconds=60, limit=10)
        for task in assign_task_list:
            start_time = current_timestamp()
            redis_key = "cloud_task_assign_monitor_{}".format(task["task_id"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} 任务ID {} 抢占分布式锁失败".format(self.get_log_tag("execute"), task["task_id"]))
                    continue
                is_locked = True
                self.dao_core_cloud_task.update_status_by_id(task_id=task["task_id"], new_status=0)
                logger.info("{} 任务ID {} 完成逻辑退回".format(self.get_log_tag("execute"), task["task_id"]))
                self.dao_core_assign_task.unassign_task(task["id"])
                logger.info("{} 任务ID {} 完成分配退回并拉黑设备".format(self.get_log_tag("execute"), task["task_id"]))
                logger.info("{} 任务ID {} 结束处理".format(self.get_log_tag("execute"), task["task_id"]))
            except:
                logger.error("{} 任务ID {} 处理发生异常 {}".format(self.get_log_tag(
                    "execute"), task["task_id"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} 任务ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), task["task_id"], current_timestamp() - start_time))
        # BIG HINT！！！
        # 拉取后大于 30 分钟没有结果的任务。完成逻辑退回
        # END
        pull_task_list = self.dao_core_assign_task.get_expire_pull(expire_seconds=30 * 60, limit=10)
        for task in pull_task_list:
            start_time = current_timestamp()
            redis_key = "cloud_task_assign_monitor_{}".format(task["task_id"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} 任务ID {} 抢占分布式锁失败".format(self.get_log_tag("execute"), task["task_id"]))
                    continue
                is_locked = True
                cloud_task = self.dao_core_cloud_task.get_task_by_id(task_id=task["task_id"])
                cloud_plan_info = self.dao_core_cloud_plan.get_plan_info_by_id(plan_id=cloud_task["plan_id"])
                pull_time = cloud_task["pull_time"]
                if json.loads(cloud_plan_info["plan_params"])["planParams"]["taskTimeout"] is not None:
                    if 60 * (5 + json.loads(cloud_plan_info["plan_params"])["planParams"]["taskTimeout"]) > (
                            current_timestamp() - pull_time):
                        continue
                else:
                    if json.loads(cloud_plan_info["plan_params"])["planParams"]["executeTimeout"] is not None:
                        if 60 * (15 + json.loads(cloud_plan_info["plan_params"])["planParams"]["executeTimeout"]) > (
                                current_timestamp() - pull_time):
                            continue
                self.dao_core_cloud_task.update_status_by_id(task_id=task["task_id"], new_status=0)
                logger.info("{} 任务ID {} 完成逻辑退回".format(self.get_log_tag("execute"), task["task_id"]))
                self.dao_core_assign_task.unassign_task(task["id"])
                logger.info("{} 任务ID {} 完成分配退回并拉黑设备".format(self.get_log_tag("execute"), task["task_id"]))
                self.dao_core_device_v2.release_device(device_id=task["device_id"])
                logger.info("{} 任务ID {} 完成设备释放 deviceId {}".format(
                    self.get_log_tag("execute"), task["task_id"], task["device_id"]))
                logger.info("{} 任务ID {} 结束处理".format(self.get_log_tag("execute"), task["task_id"]))
            except:
                logger.error("{} 任务ID {} 处理发生异常 {}".format(self.get_log_tag(
                    "execute"), task["task_id"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} 任务ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), task["task_id"], current_timestamp() - start_time))
        # BIG HINT！！！
        # 任务显示已分配或执行中，但本质没有设备实际执行。完成逻辑退回
        # END
        # 获取显示了分配但是实际并没有分配到设备的任务列表
        # 获取显示了执行中但实际可能没有设备在执行的任务列表
        task_list = self.dao_core_cloud_task.get_task_by_status(
            status=1, limit=1000) + self.dao_core_cloud_task.get_task_by_status(status=2, limit=1000)
        success_assign_task_list = self.dao_core_assign_task.get_task_from_task_list(
            id_list=[item["task_id"] for item in task_list])
        abnormal_task_list = list(set([item["task_id"] for item in task_list]) -
                                  set([item["task_id"] for item in success_assign_task_list]))
        for task_id in abnormal_task_list:
            start_time = current_timestamp()
            redis_key = "cloud_task_assign_monitor_{}".format(task_id)
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} 任务ID {} 抢占分布式锁失败".format(self.get_log_tag("execute"), task_id))
                    continue
                is_locked = True
                task_item = self.dao_core_cloud_task.get_task_by_id(task_id=task_id)
                if task_item["status"] in [1, 2]:
                    self.dao_core_cloud_task.update_status_by_id(task_id=task_id, new_status=0)
                    logger.info("{} 任务ID {} 完成逻辑退回".format(self.get_log_tag("execute"), task_id))
                    logger.info("{} 任务ID {} 结束处理".format(self.get_log_tag("execute"), task_id))
            except:
                logger.error("{} 任务ID {} 处理发生异常 {}".format(self.get_log_tag(
                    "execute"), task_id, traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} 任务ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), task_id, current_timestamp() - start_time))
