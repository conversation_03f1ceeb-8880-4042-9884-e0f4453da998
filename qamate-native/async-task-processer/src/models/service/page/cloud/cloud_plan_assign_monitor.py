# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 计划执行情况监控脚本实际处理逻辑控制

@File    :   cloud_plan_assign_monitor.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import json
import traceback
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.dao.db.core_cloud_device_task import DaoDBCoreDeviceTask
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_assign_task import DaoDBCoreAssignTask
from src.models.dao.server.core import DaoServerCore
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.models.service.data.device.device import ServiceDataDevice
from src.util.time import current_timestamp, get_delta_time_by_date
from src.lib.logger import logger

PENDING_PLAN_EXPIRE_DATE = 40


class ServicePageCloudPlanAssignMonitor:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.data_device = ServiceDataDevice()
        self.dao_core_device_task = DaoDBCoreDeviceTask()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_core_assign_task = DaoDBCoreAssignTask()
        self.dao_core_server = DaoServerCore()
        self.dao_redis_lock = DaoRedisLock()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.cloud.cloud_plan_assign_monitor.{}]".format(method_name)

    def get_avaliable_device(self, pool):
        device_list = []
        # 云端任务按照池来进行筛选空闲设备
        if 1 == pool["taskType"]:
            device_list = self.data_device.get_pool_device_list(pool_id=pool["poolId"], module_id=pool["moduleId"])
        # 本地任务需要按照计划指定设备来筛选空闲设备
        else:
            # 获取正在执行的计划中的指定设备列表
            occupy_device_set = set()
            execute_plan_list = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=pool["occupyPlanList"])
            for execute_plan in execute_plan_list:
                if isinstance(execute_plan["plan_params"], str):
                    execute_plan["plan_params"] = json.loads(execute_plan["plan_params"])
                if "deviceIdList" in execute_plan["plan_params"]["planParams"]:
                    occupy_device_set = \
                        occupy_device_set | set(execute_plan["plan_params"]["planParams"]["deviceIdList"])
            device_id_list = list(occupy_device_set)
            if 0 == len(device_id_list):
                return []
            # 获取指定列表设备状态
            device_list = self.data_device.get_device_list_by_id(id_list=device_id_list)
        if 0 == len(device_list):
            return []
        # 筛选哪些设备需要进行后面的判定
        device_info_list = self.dao_core_assign_task.get_device_info_by_id_list(
            id_list=[value['deviceId'] for value in device_list])
        device_dict = {}
        for device in device_list:
            device_dict[str(device["deviceId"])] = device
        available_device_list = []
        for device in device_info_list:
            if pool["taskType"] != device["type"]:
                continue
            # 如果设备在线且未被拉黑则是一个可以执行的设备
            # 如果设备离线但设备被分配了任务姑且认为他正在执行任务, 加入可用性判定列表
            if (
                device["task_id"] is not None
                or (
                    (
                        2 == device_dict[str(device["device_id"])]["status"]
                        or 3 == device_dict[str(device["device_id"])]["status"]
                    )
                    and 0 == device_dict[str(device["device_id"])]["operateStatus"]
                    and 1 != device["is_blank"]
                )
            ):
                available_device_list.append(device["device_id"])
            else:
                logger.info("{} 设备池ID {} 中设备 {} 不可用, 分配状态 {} 在线状态 {} 运维状态 {} 拉黑状态 {}".format(
                    self.get_log_tag("get_avaliable_device"),
                    pool["poolId"],
                    device["device_id"],
                    device["task_id"] is not None,
                    device_dict[str(device["device_id"])]["status"],
                    device_dict[str(device["device_id"])]["operateStatus"],
                    device["is_blank"]
                ))
        return list(set(available_device_list))

    def judge_abnormal_for_ui_plan(self, plan_id, device_list, device_task_list, pool):
        """
        判断 UI 计划是否需要异常处理
            需要根据设备一个个判断初始化情况
        """
        # 获取正在执行计划任务的设备
        device_info_list = self.dao_core_assign_task.get_device_info_by_id_list(id_list=device_list)
        running_device_list = []
        for device in device_info_list:
            if device["task_id"] is not None:
                running_device_list.append(device["device_id"])
        # 计算出当前计划中执行过任务且还在池中的设备
        # 找到当前没有被拉黑的设备
        available_device_list = list(set(device_list) & set(
            [value['device_id'] for value in device_task_list]) | set(running_device_list))
        if 0 == len(available_device_list):
            logger.info(
                "{} 计划ID {} 在设备池中无可用或未被拉黑设备, 跳过处理".format(self.get_log_tag("judge_abnormal_for_ui_plan"), plan_id))
            return False
        logger.info("{} 设备池ID {} 中 可用设备有 {}".format(self.get_log_tag(
            "judge_abnormal_for_ui_plan"), pool["poolId"], available_device_list))
        need_abnormal_plan = True
        # 根据设备去判断是否有可用设备
        for device_id in available_device_list:
            task_id_list = []
            for task in device_task_list:
                if task["device_id"] != device_id:
                    continue
                # 如果当前设备最后一个任务结果是正常的, 则说明该计划至少有一个设备可以正常执行任务则不需要处理该计划
                if 0 == len(task_id_list) and 0 == task["code"]:
                    logger.info("{} 设备池ID {} 中 设备 ID {} 最后一个任务状态正常, 计划进度正常".format(
                        self.get_log_tag("judge_abnormal_for_ui_plan"), pool["poolId"], device_id))
                    need_abnormal_plan = False
                    break
                # 获取最后执行的 4 个任务，因为我们每个设备的初始化任务至少执行 3 遍都失败则为异常情况
                task_id_list.append(task["task_id"])
                if len(task_id_list) >= 4:
                    break
            if len(task_id_list) < 4:
                logger.info("{} 设备池ID {} 中 设备 ID {} 执行任务不足 4 个, 计划进度正常".format(
                            self.get_log_tag("judge_abnormal_for_ui_plan"), pool["poolId"], device_id))
                need_abnormal_plan = False
            if len(set(task_id_list)) > 1:
                logger.info("{} 设备池ID {} 中 设备 ID {} 最后执行的 4 个任务非重复任务, 计划进度正常".format(
                            self.get_log_tag("judge_abnormal_for_ui_plan"), pool["poolId"], device_id))
                need_abnormal_plan = False
            # 如果执行的计划已经不需要异常了则直接跳出循环
            if False == need_abnormal_plan:
                break
            if 0 == self.dao_core_cloud_task.get_task_by_id(task_id=task_id_list[0])["type"]:
                logger.info("{} 设备池ID {} 中 设备 ID {} 最后执行的 1 个任务都是正常任务, 计划进度正常".format(
                            self.get_log_tag("judge_abnormal_for_ui_plan"), pool["poolId"], device_id))
                need_abnormal_plan = False
                break
        return need_abnormal_plan

    def judge_abnormal_for_server_plan(self, plan_id, device_task_list):
        """
        判断 Server 计划是否需要异常处理
            将所有设备视为一体判断初始化情况
        """
        need_abnormal_plan = True
        # 判断所有任务
        task_id_list = []
        for task in device_task_list:
            # 如果当前设备最后一个任务结果是正常的, 则说明该计划至少有一个设备可以正常执行任务则不需要处理该计划
            if 0 == len(task_id_list) and 0 == task["code"]:
                logger.info("{} 计划 ID {} 最后一个任务状态正常, 计划进度正常".format(
                    self.get_log_tag("judge_abnormal_for_server_plan"), plan_id))
                need_abnormal_plan = False
                break
            # 获取最后执行的 4 个任务，因为我们每个设备的初始化任务至少执行 3 遍都失败则为异常情况
            task_id_list.append(task["task_id"])
            if len(task_id_list) >= 4:
                break
        if len(task_id_list) < 4:
            logger.info("{} 计划 ID {} 执行任务不足 4 个, 计划进度正常".format(
                        self.get_log_tag("judge_abnormal_for_server_plan"), plan_id))
            need_abnormal_plan = False
        if len(set(task_id_list)) > 1:
            logger.info("{} 计划 ID {} 最后执行的 4 个任务非重复任务, 计划进度正常".format(
                        self.get_log_tag("judge_abnormal_for_server_plan"), plan_id))
            need_abnormal_plan = False
        # 如果执行的计划已经不需要异常了则直接跳出循环
        if need_abnormal_plan:
            if 0 == self.dao_core_cloud_task.get_task_by_id(task_id=task_id_list[0])["type"]:
                logger.info("{} 计划 ID {} 最后执行的 1 个任务都是正常任务, 计划进度正常".format(
                            self.get_log_tag("judge_abnormal_for_server_plan"), plan_id))
                need_abnormal_plan = False
        return need_abnormal_plan

    def monitor_plan_hang(self):
        """
        仅防止设备一直在执行初始化任务, 无法达成正常任务条件. 暂时不考虑没有设备执行计划的情况
        """
        pool_list = self.data_device.get_whole_pool_list()
        # 处理云端任务
        for pool in pool_list:
            start_time = current_timestamp()
            logger.info("{} 准备处理 ID {} 设备池".format(self.get_log_tag("monitor_plan_hang"), pool["poolId"]))
            if len(pool["occupyPlanList"]) == 0:
                logger.info("{} ID {} 设备池无执行计划, 跳过".format(self.get_log_tag("monitor_plan_hang"), pool["poolId"]))
                continue
            # 开始抢占分布式锁准备处理池子
            redis_key = "cloud_plan_assign_monitor_pool_{}".format(pool["poolId"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} ID {} 设备池抢占分布式锁失败".format(self.get_log_tag("monitor_plan_hang"), pool["poolId"]))
                    continue
                is_locked = True
                # 找到设备池中所有的在线设备（无需关心设备是否可用）
                device_list = self.get_avaliable_device(pool)
                if 0 == len(device_list):
                    logger.info("{} ID {} 设备池无在线设备, 不对内部计划做任何处理".format(
                        self.get_log_tag("monitor_plan_hang"), pool["poolId"]))
                # 处理设备池中所有正在执行的计划
                else:
                    execute_plan_list = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=pool["occupyPlanList"])
                    for execute_plan in execute_plan_list:
                        plan_id = execute_plan["id"]
                        if isinstance(execute_plan["plan_params"], str):
                            execute_plan["plan_params"] = json.loads(execute_plan["plan_params"])
                        # 获取这个计划所有执行过的设备任务
                        device_task_list = self.dao_core_device_task.get_device_task_by_plan_id(plan_id=plan_id)
                        need_abnormal_plan = False
                        # UI 调度（所有设备都需要独立进行初始化操作）——默认调度方式
                        if (
                            "assignType" not in execute_plan["plan_params"]["planParams"]
                            or int(execute_plan["plan_params"]["planParams"]["assignType"]) == 1
                        ):
                            need_abnormal_plan = self.judge_abnormal_for_ui_plan(
                                plan_id, device_list, device_task_list, pool)
                        # Server 调度（所有设备视为一起的，不需要单独初始化）
                        elif int(execute_plan["plan_params"]["planParams"]["assignType"]) == 2:
                            need_abnormal_plan = self.judge_abnormal_for_server_plan(plan_id, device_task_list)
                        # 异常计划处理
                        if need_abnormal_plan:
                            logger.info("{} 设备池ID {} 中 计划 ID {} 需要异常处理".format(
                                self.get_log_tag("monitor_plan_hang"), pool["poolId"], plan_id))
                            self.data_cloud_plan.except_plan(plan_id=plan_id, exception_msg="设备全都不满足任务初始化条件")
                logger.info("{} ID {} 设备池结束处理".format(self.get_log_tag("monitor_plan_hang"), pool["poolId"]))
            except:
                logger.error("{} ID {} 处理发生异常 {}".format(self.get_log_tag(
                    "monitor_plan_hang"), pool["poolId"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "monitor_plan_hang"), pool["poolId"], current_timestamp()-start_time))

    def monitor_plan_expired(self):
        """
        取消排队超过 一个月 的计划
        """
        plan_list = self.dao_core_cloud_plan.get_plan_by_status_and_expire_time(
            status=1, expire_time=get_delta_time_by_date(delta_date=PENDING_PLAN_EXPIRE_DATE))
        for plan in plan_list:
            start_time = current_timestamp()
            redis_key = "cloud_plan_assign_monitor_plan_{}".format(plan["plan_id"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} ID {} 计划抢占分布式锁失败".format(
                        self.get_log_tag("monitor_plan_expired"), plan["plan_id"]))
                    continue
                is_locked = True
                self.dao_core_server.cancel_cloud_plan(plan_id=plan["plan_id"])
                logger.info("{} ID {} 计划结束处理".format(self.get_log_tag("monitor_plan_expired"), plan["plan_id"]))
            except:
                logger.error("{} ID {} 处理发生异常 {}".format(self.get_log_tag(
                    "monitor_plan_expired"), plan["plan_id"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "monitor_plan_expired"), plan["plan_id"], current_timestamp()-start_time))

    def execute(self):
        """
        执行主函数
        """
        self.monitor_plan_hang()
        self.monitor_plan_expired()
