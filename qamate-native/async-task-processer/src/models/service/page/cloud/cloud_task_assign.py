# -*- encoding: utf-8 -*-
"""
@Desc    :   Cloud 任务执行分配脚本实际处理逻辑控制

@File    :   cloud_task_assign.py
@Time    :   2024-07-30
<AUTHOR>   <EMAIL>
"""
import json
import traceback
from src.models.service.data.cloud.cloud_plan import ServiceDataCloudPlan
from src.models.service.data.device.device import ServiceDataDevice
from src.models.dao.db.core_cloud_plan import DaoDBCoreCloudPlan
from src.models.dao.db.core_cloud_task import DaoDBCoreCloudTask
from src.models.dao.db.core_cloud_assign_task import DaoDBCoreAssignTask
from src.models.dao.db.core_cloud_device_task import DaoDBCoreDeviceTask
from src.models.dao.redis.redis_lock import DaoRedisLock
from src.models.dao.server.core import DaoServerCore
from src.util.time import current_timestamp
from src.lib.logger import logger


class ServicePageCloudTaskAssign:
    """
    脚本执行类
    """

    def __init__(self):
        """
        初始化函数, 用于初始化各个DAO对象和BOS服务。
        """
        self.data_cloud_plan = ServiceDataCloudPlan()
        self.data_device = ServiceDataDevice()
        self.dao_core_cloud_plan = DaoDBCoreCloudPlan()
        self.dao_core_cloud_task = DaoDBCoreCloudTask()
        self.dao_core_assign_task = DaoDBCoreAssignTask()
        self.dao_core_device_task = DaoDBCoreDeviceTask()
        self.dao_redis_lock = DaoRedisLock()
        self.dao_server_core = DaoServerCore()

    def get_log_tag(self, method_name):
        """
        初始化日志头信息
        """
        return "[models.service.page.cloud.cloud_task_assign.{}]".format(method_name)

    def assign_task(self, device_id, task_id, pool_type):
        """
        完成设备任务分配逻辑
        """
        # 如果设备不存在, 则插入一条记录
        if self.dao_core_assign_task.get_device_info_by_id_type(device_id=device_id, type=pool_type) is None:
            self.dao_core_assign_task.insert_device(device_id=device_id, type=pool_type)
        # 修改设备分配信息
        self.dao_core_assign_task.assign_task(device_id=device_id, type=pool_type, task_id=task_id)
        # 修改任务状态
        self.dao_core_cloud_task.update_status_by_id(task_id=task_id, new_status=1)

    def exclude_blank_device(self, device_list, type):
        """
        剔除被拉黑或已分配任务的设备
        """
        device_info_list = self.dao_core_assign_task.get_device_info_by_id_list(id_list=device_list)
        for device in device_info_list:
            if type != device["type"]:
                continue
            if device["device_id"] in device_list and (device["task_id"] is not None or 1 == device["is_blank"]):
                device_list.remove(device["device_id"])
        return device_list

    def get_avaliable_device(self, pool):
        available_device_list = []
        execute_plan_list = []
        device_list = []
        # 云端任务按照池来进行筛选空闲设备
        if 1 == pool["taskType"]:
            device_list = self.data_device.get_pool_device_list(pool_id=pool["poolId"], module_id=pool["moduleId"])
        # 本地任务需要按照计划指定设备来筛选空闲设备
        else:
            # 获取正在执行的计划中的指定设备列表
            occupy_device_set = set()
            execute_plan_list = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=pool["occupyPlanList"])
            for execute_plan in execute_plan_list:
                if isinstance(execute_plan["plan_params"], str):
                    execute_plan["plan_params"] = json.loads(execute_plan["plan_params"])
                if "deviceIdList" in execute_plan["plan_params"]["planParams"]:
                    occupy_device_set = occupy_device_set | set(
                        execute_plan["plan_params"]["planParams"]["deviceIdList"])
            device_id_list = list(occupy_device_set)
            if 0 == len(device_id_list):
                return [], execute_plan_list
            # 获取指定列表设备状态
            device_list = self.data_device.get_device_list_by_id(id_list=device_id_list)
        # 只筛选空闲设备
        for device in device_list:
            if 2 == device["status"] and 0 == device["operateStatus"]:
                available_device_list.append(device["deviceId"])
        # 剔除被拉黑或已分配任务的设备
        available_device_list = self.exclude_blank_device(available_device_list, pool["taskType"])
        return available_device_list, execute_plan_list

    def sort_task_by_dependency(self, task_list):
        """
        根据任务的依赖情况进行归类
        """
        return sorted(
            task_list,
            key=lambda task: (task["is_finish"], -task["retry_times"], task['setup_list'],
                              task['before_list'], task['after_list'], task["task_id"])
        )

    def handle_device_dependency(self, device_task_list, task_dict):
        """
        计算出这个设备当前生效的依赖关系
        """
        dependency_info = {
            "setup_id": None,
            "before_id": None,
            "after_id": None,
            "lastest_task_id": None
        }
        for task in device_task_list:
            # 记录设备执行成功的依赖任务
            if 0 == task["code"]:
                # 前置任务会作废所有前面执行过的所有任务
                if 2 == task_dict[str(task["task_id"])]["type"]:
                    dependency_info["setup_id"] = task["task_id"]
                    break
                # before 任务只记录最后一个
                elif 1 == task_dict[str(task["task_id"])]["type"]:
                    if dependency_info["before_id"] is None:
                        dependency_info["before_id"] = task["task_id"]
                # after 任务只记录最后一个 且 一定得在 before 之后
                elif 4 == task_dict[str(task["task_id"])]["type"]:
                    if dependency_info["before_id"] is None and dependency_info["after_id"] is None:
                        dependency_info["after_id"] = task["task_id"]
            # 记录最后一个执行的任务
            if 0 == task_dict[str(task["task_id"])]["type"]:
                if dependency_info["lastest_task_id"] is None:
                    dependency_info["lastest_task_id"] = task["task_id"]
        return dependency_info

    def execute(self):
        """
        执行主函数
        """
        pool_list = self.data_device.get_ui_pool_list()
        # 处理云端任务
        for pool in pool_list:
            start_time = current_timestamp()
            logger.info("{} 准备处理 ID {} 设备池".format(self.get_log_tag("execute"), pool["poolId"]))
            if len(pool["occupyPlanList"]) == 0:
                logger.info("{} ID {} 设备池无执行计划, 跳过".format(self.get_log_tag("execute"), pool["poolId"]))
                continue
            # 开始抢占分布式锁准备处理池子
            redis_key = "cloud_task_assign_pool_{}".format(pool["poolId"])
            is_locked = False
            try:
                if False == self.dao_redis_lock.lock(redis_key, 60):
                    logger.warning("{} ID {} 设备池抢占分布式锁失败".format(self.get_log_tag("execute"), pool["poolId"]))
                    continue
                is_locked = True
                # 获取池中是否有正在空闲的设备
                available_device_list, execute_plan_list = self.get_avaliable_device(pool)
                if 0 == len(available_device_list):
                    logger.info("{} ID {} 设备池无空闲设备或所有设备被拉黑, 跳过".format(
                        self.get_log_tag("execute"), pool["poolId"]))
                    continue
                # 获取正在执行的计划并按需分配
                if 0 == len(execute_plan_list):
                    execute_plan_list = self.dao_core_cloud_plan.get_plan_by_id_list(id_list=pool["occupyPlanList"])
                assigned_task_list = []
                for device_id in available_device_list:
                    this_device_is_assign = False
                    logger.info("{} 设备 ID {} 开始寻找计划与任务".format(self.get_log_tag("execute"), device_id))
                    for execute_plan in execute_plan_list:
                        if isinstance(execute_plan["plan_params"], str):
                            execute_plan["plan_params"] = json.loads(execute_plan["plan_params"])
                        # 判断当前计划是否可执行
                        if False == self.data_cloud_plan.is_plan_can_execute_time(
                            plan_params=execute_plan["plan_params"]
                        ):
                            continue
                        # 找到一个可以匹配的计划
                        if False == self.data_cloud_plan.is_device_match_plan(
                            plan_params=execute_plan["plan_params"],
                            device_id=device_id
                        ):
                            continue
                        logger.info("{} 设备 ID {} 匹配计划ID {} 成功".format(
                            self.get_log_tag("execute"), device_id, execute_plan["id"]))
                        # 加载计划中所有的任务并且找到一个预期执行的任务
                        if "task_list" not in execute_plan:
                            execute_plan["task_list"] = self.dao_core_cloud_task.get_task_by_plan_id(
                                plan_id=execute_plan["id"])
                            execute_plan["task_id_list"] = [task["task_id"] for task in execute_plan["task_list"]]
                            execute_plan["task_dict"] = {}
                            for task in execute_plan["task_list"]:
                                execute_plan["task_dict"][str(task["task_id"])] = task
                                task_info = json.loads(task["task_info"])
                                execute_plan["task_dict"][str(task["task_id"])]["is_finish"] = 3 == task["type"]
                                execute_plan["task_dict"][str(task["task_id"])]["setup_list"] = []
                                execute_plan["task_dict"][str(task["task_id"])]["before_list"] = []
                                execute_plan["task_dict"][str(task["task_id"])]["after_list"] = []
                                if "setupList" in task_info:
                                    execute_plan["task_dict"][str(task["task_id"])]["setup_list"] = \
                                        task_info["setupList"]
                                if "beforeAllList" in task_info:
                                    execute_plan["task_dict"][str(task["task_id"])]["before_list"] = \
                                        task_info["beforeAllList"]
                                if "afterAllList" in task_info:
                                    execute_plan["task_dict"][str(task["task_id"])]["after_list"] = \
                                        task_info["afterAllList"]
                            execute_plan["task_list"] = self.sort_task_by_dependency(execute_plan["task_dict"].values())
                        # 判断是否满足 QPS 需求
                        if (
                            "qps" in execute_plan["plan_params"]["planParams"]
                            and int(execute_plan["plan_params"]["planParams"]["qps"]) > 0
                        ):
                            now_plan_qps = int(self.dao_core_assign_task.get_cound_by_task_id_list(
                                id_list=execute_plan["task_id_list"]))
                            if now_plan_qps >= int(execute_plan["plan_params"]["planParams"]["qps"]):
                                logger.info("{} 计划ID {} 当前QPS {} 已超过最大值, 放弃该计划匹配".format(
                                    self.get_log_tag("execute"), execute_plan["id"], now_plan_qps))
                                continue
                        # 加载设备在该计划中执行过的任务情况（前置、初始化、后置、最近的普通任务）
                        device_dependency_task_info = {}
                        # UI 调度（所有设备都需要独立进行初始化操作）——默认调度方式
                        if (
                            "assignType" not in execute_plan["plan_params"]["planParams"]
                            or int(execute_plan["plan_params"]["planParams"]["assignType"]) == 1
                        ):
                            device_dependency_task_info = self.handle_device_dependency(
                                self.dao_core_device_task.get_device_task_by_id(
                                    device_id=device_id, plan_id=execute_plan["id"]),
                                execute_plan["task_dict"]
                            )
                        # Server 调度（所有设备视为一起的，不需要单独初始化）
                        elif int(execute_plan["plan_params"]["planParams"]["assignType"]) == 2:
                            device_dependency_task_info = self.handle_device_dependency(
                                self.dao_core_device_task.get_device_task_by_plan_id(plan_id=execute_plan["id"]),
                                execute_plan["task_dict"]
                            )
                        # 匹配任务进行处理
                        for task in execute_plan["task_list"]:
                            # 只处理 后置任务 或（正常任务和正在排队中的任务）
                            if (
                                (0 != task["type"] and 3 != task["type"])
                                or 0 != task["status"] or task["task_id"] in assigned_task_list
                            ):
                                continue
                            logger.info("{} 设备 ID {} 匹配任务ID {} 成功".format(
                                self.get_log_tag("execute"), device_id, task["task_id"]))
                            # 先判断这个任务是否需要先把之前的后处理任务执行了
                            # 1. 这个任务之前执行过别的正常任务
                            if device_dependency_task_info["lastest_task_id"] is not None:
                                # 2. 这个任务需要的前置、初始化和之前的正常任务完全不一样
                                task_setup_id = next(
                                    iter(execute_plan["task_dict"][str(task["task_id"])]["setup_list"]), None)
                                last_setup_id = next(iter(execute_plan["task_dict"][str(
                                    device_dependency_task_info["lastest_task_id"])]["setup_list"]), None)
                                task_before_id = next(
                                    iter(execute_plan["task_dict"][str(task["task_id"])]["before_list"]), None)
                                last_before_id = next(iter(execute_plan["task_dict"][str(
                                    device_dependency_task_info["lastest_task_id"])]["before_list"]), None)
                                if task_before_id != last_before_id or task_setup_id != last_setup_id:
                                    # 3. 之前的正常任务的 后置 任务不为空
                                    if len(execute_plan["task_dict"][str(
                                            device_dependency_task_info["lastest_task_id"])]["after_list"]) > 0:
                                        # 4. 没有执行过后处理任务
                                        after_task_id = execute_plan["task_dict"][str(
                                            device_dependency_task_info["lastest_task_id"])]["after_list"][0]
                                        if after_task_id != device_dependency_task_info["after_id"]:
                                            logger.info("{} 设备 ID {} 需要执行任务ID {} 的后处理任务 {}".format(
                                                self.get_log_tag("execute"),
                                                device_id,
                                                device_dependency_task_info["lastest_task_id"],
                                                after_task_id))
                                            self.assign_task(device_id, after_task_id, pool["taskType"])
                                            this_device_is_assign = True
                                            break
                            # 判定这个任务是不是需要执行前置任务
                            if (
                                len(execute_plan["task_dict"][str(task["task_id"])]["setup_list"]) > 0
                                and execute_plan["task_dict"][str(task["task_id"])]["setup_list"][0]
                                    != device_dependency_task_info["setup_id"]
                            ):
                                setup_task_id = execute_plan["task_dict"][str(task["task_id"])]["setup_list"][0]
                                logger.info("{} 设备 ID {} 需要执行任务ID {} 的前置任务 {}".format(
                                    self.get_log_tag("execute"), device_id, task["task_id"], setup_task_id))
                                self.assign_task(device_id, setup_task_id, pool["taskType"])
                                this_device_is_assign = True
                                break
                            # 判定这个任务是不是需要执行初始化任务
                            if (
                                len(execute_plan["task_dict"][str(task["task_id"])]["before_list"]) > 0
                                and execute_plan["task_dict"][str(task["task_id"])]["before_list"][0]
                                    != device_dependency_task_info["before_id"]
                            ):
                                before_task_id = execute_plan["task_dict"][str(task["task_id"])]["before_list"][0]
                                logger.info("{} 设备 ID {} 需要执行任务ID {} 的初始化任务 {}".format(
                                    self.get_log_tag("execute"), device_id, task["task_id"], before_task_id))
                                self.assign_task(device_id, before_task_id, pool["taskType"])
                                this_device_is_assign = True
                                break
                            # 判定这个任务是不是后置任务，如果是则代表满足了结束条件
                            if 3 == task["type"]:
                                logger.info("{} 计划 ID {} 满足结束条件".format(
                                    self.get_log_tag("execute"), execute_plan["id"]))
                                self.dao_core_cloud_task.update_status_by_id(task_id=task["task_id"], new_status=3)
                                break
                            # 直接执行该任务
                            logger.info("{} 设备 ID {} 满足执行条件, 成功分配".format(self.get_log_tag("execute"), device_id))
                            assigned_task_list.append(task["task_id"])
                            self.assign_task(device_id, task["task_id"], pool["taskType"])
                            this_device_is_assign = True
                            break
                        if this_device_is_assign:
                            logger.info("{} 设备 ID {} 已分配任务完毕".format(self.get_log_tag("execute"), device_id))
                            break
                logger.info("{} ID {} 设备池结束处理".format(self.get_log_tag("execute"), pool["poolId"]))
            except:
                logger.error("{} ID {} 处理发生异常 {}".format(self.get_log_tag(
                    "execute"), pool["poolId"], traceback.format_exc().replace('\n', ' ')))
            finally:
                if is_locked:
                    self.dao_redis_lock.unlock(redis_key)
                logger.info("{} ID {} 完成释放分布式锁 总耗时 {} ms".format(self.get_log_tag(
                    "execute"), pool["poolId"], current_timestamp()-start_time))
