# -*- encoding: utf-8 -*-
"""
@Desc    :   hi utils

@File    :   hi.py
@Time    :   2024-07-29
<AUTHOR>   wang<PERSON><EMAIL>
"""
import json
import requests


def send_hi_message(webhook, params):
    """
    发送如流消息
    """
    result = requests.post(
        webhook,
        data=params,
        headers={
            "Content-Type": "application/json"
        },
        timeout=5
    )

    result = json.loads(result.text)
    if "errcode" not in result or 0 != result["errcode"]:
        raise Exception("发送消息失败 {} {}".format(result["errcode"], result["errmsg"]))