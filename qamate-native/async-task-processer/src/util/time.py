# -*- encoding: utf-8 -*-
"""
@Desc    :   time utils

@File    :   time.py
@Time    :   2024-07-29
<AUTHOR>   wang<PERSON><PERSON><PERSON><PERSON>@baidu.com
"""
import time
from datetime import datetime, timedelta


def current_timestamp(is_ms=True):
    """
    获取当前时间戳

    :return: 时间戳 单位 ms
    """
    if is_ms:
        return int(time.time() * 1000)
    else:
        return int(time.time())


def trans_time_by_format(date, format):
    """
    格式化时间字符串转为时间戳

    :param date: 时间字符串
    :param format: 时间格式

    :return: 时间戳 单位 s
    """
    time_array = time.strptime(date, format)
    timestamp = time.mktime(time_array)
    return timestamp


def trans_date_by_format(timestamp, format):
    """
    时间戳为格式化时间字符串

    :param time_stamp: 时间戳 单位 s
    :param format: 时间格式
    :return: 日期字符串
    """
    time_array = time.localtime(timestamp)
    date_string = time.strftime("%Y-%m-%d", time_array)
    return date_string


def this_monday(today, format):
    """
    获取本周周一日期

    :param today: 当天日期
    :return: 返回周一的日期
    """
    today = datetime.strptime(str(today), format)
    return datetime.strftime(today - timedelta(today.weekday()), format)


def this_sunday(today, format):
    """
    获取本周周日日期

    :param today: 当天日期
    :return: 返回周日日期
    """
    today = datetime.strptime(str(today), format)
    return datetime.strftime(today + timedelta(7 - today.weekday() - 1), format)


def get_timestamp_with_gap(timestamp, gap_day):
    """
    获取指定时间戳前后的时间戳

    :param timestamp: 时间戳
    :param gap: 间隔天数
    :return: 返回时间戳
    """
    return timestamp - (3600 * 24 * gap_day)


def compare_time_only_time_non_date(x, y):
    """
    比较时间大小, 不考虑日期仅考虑小时和分钟

    :param x: X 时间 (datetime)
    :param y: Y 时间 (datetime)
    :return: X <= Y 返回 True
    """
    if x.hour < y.hour:
        return True
    if x.hour == y.hour:
        if x.minute < y.minute:
            return True
        if x.minute == y.minute and x.second < y.second:
            return True
        return False
    return False


def get_delta_time_by_date(delta_date):
    """
    获取前 N 天的零点时间戳

    :param delta_date: N 天
    :return: timestamp
    """
    time_date = datetime.now() - timedelta(days=delta_date)
    time_date_zero = datetime(time_date.year, time_date.month, time_date.day, 0, 0, 0)
    return int(time.mktime(time_date_zero.timetuple()))
