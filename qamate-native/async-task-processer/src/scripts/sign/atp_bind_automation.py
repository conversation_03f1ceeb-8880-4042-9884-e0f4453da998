# -*- encoding: utf-8 -*-
"""
@Desc    :   脑图同步自动化脚本

@File    :   atp_bind_automation.py
@Time    :   2024-07-16
<AUTHOR>   <EMAIL>
"""
import os
import sys


if __name__ == "__main__":
    # 脚本载入项目根路径
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))
    from src.scripts.script_handler import ScriptHandler

    class ScriptsAtpBindAutomation(ScriptHandler):
        """
        ATP 脑图同步自动化入口类
        """

        def __init__(self):
            """
            初始化函数, 用于初始化类的属性和方法
            """
            super().__init__()
            from src.models.service.page.sign.atp_bind_automation import ServicePageBindAutomation
            self.service = ServicePageBindAutomation()
    # 执行脚本
    script = ScriptsAtpBindAutomation()
    ###
    # 该脚本已无用！已停止运行
    ###
    # script.execute()
