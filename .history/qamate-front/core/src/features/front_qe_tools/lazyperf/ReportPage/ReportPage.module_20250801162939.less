.content {
    padding: 20px;
    min-height: 100vh;
    overflow-y: auto;
}
.taskPage {
    padding: 20px 40px;

    .operateHeader {
        width: 100%;
        margin-bottom: 14px;
        display: flex;
        justify-content: space-between;
    }
}

.reportPageContent {
    border: 1px solid red;
    background-color: #fff;
    padding: 0 25px 20px;
    min-height: calc(100vh - 40px); /* 减去外层 padding */
}