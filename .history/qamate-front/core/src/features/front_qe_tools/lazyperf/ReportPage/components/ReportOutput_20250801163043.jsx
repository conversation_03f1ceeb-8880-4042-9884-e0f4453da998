
import { useState } from 'react';
import { But<PERSON>, Typo<PERSON>, Divider, message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import html2canvas from 'html2canvas';
import ReportModule from './ReportModule';

const { Title } = Typography;

const ReportOutput = ({
    reportTitle,
    reportNames,
    reportDataArray,
    reportDescList,
    reportTableDataList,
    onUpdateTitle,
    onUpdateDescription,
    onDeleteModule,
    onMoveUp,
    onMoveDown
}) => {
    const [downloadLoading, setDownloadLoading] = useState(false);

    // 下载报告
    const downloadReport = async () => {
        const element = document.querySelector('.report-container');
        if (!element) {
            message.error('报告内容未找到');
            return;
        }
        setDownloadLoading(true);
        try {
            const canvas = await html2canvas(element, {
                useCORS: true,
                scale: 2,
                backgroundColor: '#ffffff'
            });

            const image = canvas.toDataURL('image/jpeg', 1.0);
            const link = document.createElement('a');
            link.href = image;
            link.download = `${reportTitle || '性能评测报告'}.jpg`;
            link.click();
        } finally {
            setDownloadLoading(false);
        }
    };

    const showDownloadButton = reportTitle || reportNames.length > 0;

    return (
        <div style={{ height: '100%',overflow: 'auto'}}>
            <div className="report-container" style={{ padding: 20 }}>
                {/* 报告标题 */}
                {reportTitle && (
                    <div style={{ paddingTop: 20 }}>
                        <Title level={1} style={{ textAlign: 'center', fontSize: 26 }}>
                            {reportTitle}
                        </Title>
                        <Divider style={{ margin: 0, padding: 0 }} />
                    </div>
                )}

                {/* 报告模块列表 */}
                {reportNames.map((name, index) => (
                    <div key={index}>
                        <ReportModule
                            name={name}
                            index={index}
                            description={reportDescList[index] || ''}
                            reportData={reportDataArray[index]}
                            reportTableData={reportTableDataList[index]}
                            onUpdateTitle={onUpdateTitle}
                            onUpdateDescription={onUpdateDescription}
                            onDelete={onDeleteModule}
                            onMoveUp={onMoveUp}
                            onMoveDown={onMoveDown}
                        />
                        {index < reportNames.length - 1 && <Divider />}
                    </div>
                ))}
            </div>

            {/* 下载按钮 */}
            {showDownloadButton && (
                <div
                    style={{
                        width: '100%',
                        textAlign: 'center',
                        marginTop: 30,
                        paddingBottom: 20
                    }}
                >
                    <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        loading={downloadLoading}
                        onClick={downloadReport}
                    >
                        {downloadLoading ? '下载中...' : '下载报告'}
                    </Button>
                </div>
            )}
        </div>
    );
};

export default ReportOutput;
