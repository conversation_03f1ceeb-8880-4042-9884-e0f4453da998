/**
 * @file ReportPage.jsx 数据评测报告页面
 * @create 迁移自旧版本 2025.07.31
 */
import React, { useState, useCallback, useEffect } from 'react';
import { Layout, message } from 'antd';
import { getSpeedRoundReport } from 'COMMON/api/front_qe_tools/lazyperf';
import InfoTitle from './components/InfoTitle';
import InfoFilter from './components/InfoFilter';
import ReportOutput from './components/ReportOutput';

const { Content } = Layout;
const { TextArea } = Input;
const { Title } = Typography;
const { Option } = Select;

const ReportPage = (props) => {
    const { curTask } = props; // 从父组件传入当前任务

    // 状态管理 - 按照旧代码的数据结构
    const [reportTitle, setReportTitle] = useState('性能评测报告');
    const [reportNames, setReportNames] = useState([]); // 报告模块名称列表
    const [reportDataArray, setReportDataArray] = useState([]); // 报告数据列表
    const [reportDescList, setReportDescList] = useState([]); // 报告描述列表
    const [reportTableDataList, setReportTableDataList] = useState([]); // 报告表格数据列表
    const [reportInfoList, setReportInfoList] = useState([]); // 报告筛选数据列表

    // 筛选选项状态
    const [reportTaskOption, setReportTaskOption] = useState({
        caseId: null,
        sceneId: null,
        modalIndex: null
    });

    // 场景列表状态
    const [sceneList, setSceneList] = useState([]);

    const [loading, setLoading] = useState(false);

    // 当用例改变时，更新场景列表（参考 FramePage 的实现）
    useEffect(() => {
        if (!reportTaskOption.caseId || !curTask) {
            setSceneList([]);
            return;
        }

        const curCaseNode = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
            (item) => item?.caseNodeId === reportTaskOption.caseId
        );
        const newSceneList = curCaseNode?.sceneList || [];
        setSceneList(newSceneList);

        // 如果当前选中的场景不在新的场景列表中，清空场景选择
        if (reportTaskOption.sceneId && !newSceneList.find(scene => scene.id === reportTaskOption.sceneId)) {
            setReportTaskOption(prev => ({
                ...prev,
                sceneId: null
            }));
        }
    }, [reportTaskOption.caseId, curTask, reportTaskOption.sceneId]);

    // 添加新模块 - 纯前端操作，不调用API
    const addNewModule = () => {
        const newReportNames = [...reportNames, `报告 ${reportNames.length + 1}`];
        const newReportDataArray = [...reportDataArray, []];
        const newReportDescList = [...reportDescList, ''];
        const newReportTableDataList = [...reportTableDataList, []];
        const newReportInfoList = [...reportInfoList, []];

        setReportNames(newReportNames);
        setReportDataArray(newReportDataArray);
        setReportDescList(newReportDescList);
        setReportTableDataList(newReportTableDataList);
        setReportInfoList(newReportInfoList);

        message.success('新模块添加成功');
    };

    // 上移模块
    const moveModuleUp = (index) => {
        if (index === 0) {
            message.warning('已经是第一个模块');
            return;
        }

        const newReportNames = [...reportNames];
        const newReportDataArray = [...reportDataArray];
        const newReportDescList = [...reportDescList];
        const newReportTableDataList = [...reportTableDataList];
        const newReportInfoList = [...reportInfoList];

        // 交换当前模块和上一个模块的位置
        [newReportNames[index], newReportNames[index - 1]] = [newReportNames[index - 1], newReportNames[index]];
        [newReportDataArray[index], newReportDataArray[index - 1]] = [newReportDataArray[index - 1], newReportDataArray[index]];
        [newReportDescList[index], newReportDescList[index - 1]] = [newReportDescList[index - 1], newReportDescList[index]];
        [newReportTableDataList[index], newReportTableDataList[index - 1]] = [newReportTableDataList[index - 1], newReportTableDataList[index]];
        [newReportInfoList[index], newReportInfoList[index - 1]] = [newReportInfoList[index - 1], newReportInfoList[index]];

        setReportNames(newReportNames);
        setReportDataArray(newReportDataArray);
        setReportDescList(newReportDescList);
        setReportTableDataList(newReportTableDataList);
        setReportInfoList(newReportInfoList);
    };

    // 下移模块
    const moveModuleDown = (index) => {
        if (index === reportNames.length - 1) {
            message.warning('已经是最后一个模块');
            return;
        }

        const newReportNames = [...reportNames];
        const newReportDataArray = [...reportDataArray];
        const newReportDescList = [...reportDescList];
        const newReportTableDataList = [...reportTableDataList];
        const newReportInfoList = [...reportInfoList];

        // 交换当前模块和下一个模块的位置
        [newReportNames[index], newReportNames[index + 1]] = [newReportNames[index + 1], newReportNames[index]];
        [newReportDataArray[index], newReportDataArray[index + 1]] = [newReportDataArray[index + 1], newReportDataArray[index]];
        [newReportDescList[index], newReportDescList[index + 1]] = [newReportDescList[index + 1], newReportDescList[index]];
        [newReportTableDataList[index], newReportTableDataList[index + 1]] = [newReportTableDataList[index + 1], newReportTableDataList[index]];
        [newReportInfoList[index], newReportInfoList[index + 1]] = [newReportInfoList[index + 1], newReportInfoList[index]];

        setReportNames(newReportNames);
        setReportDataArray(newReportDataArray);
        setReportDescList(newReportDescList);
        setReportTableDataList(newReportTableDataList);
        setReportInfoList(newReportInfoList);
    };

    // 获取报告数据并添加到指定模块
    const addDataToReport = useCallback(async () => {
        const { caseId, sceneId, modalIndex } = reportTaskOption;

        if (!caseId) {
            message.warning('用例不得为空');
            return;
        }
        if (!sceneId) {
            message.warning('场景不得为空');
            return;
        }
        if (modalIndex === null || modalIndex === undefined) {
            message.warning('报告模块不得为空');
            return;
        }
        if (!curTask?.planId) {
            message.warning('任务信息不完整');
            return;
        }

        setLoading(true);
        try {
            const params = {
                planId: parseInt(curTask.planId),
                caseNodeId: parseInt(caseId),
                sceneId: parseInt(sceneId)
            };

            const response = await getSpeedRoundReport(params);

            if (response && response.code === 0) {
                const reportData = response.data;

                // 更新对应模块的数据
                const newReportDataArray = [...reportDataArray];
                const newReportTableDataList = [...reportTableDataList];
                const newReportInfoList = [...reportInfoList];

                // 构造报告数据结构（简化版）
                const processedData = {
                    min: reportData.min,
                    max: reportData.max,
                    avg: reportData.avg,
                    confidence: reportData.confidence,
                    totalCount: reportData.totalCount,
                    actualCount: reportData.actualCount
                };

                // 构造表格数据 - 从 curTask 中获取用例和场景信息
                const selectedCase = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
                    item => item?.caseNodeId === caseId
                );
                const selectedScene = sceneList.find(sceneItem => sceneItem.id === sceneId);

                const tableDataItem = {
                    key: `${curTask.planId}_${caseId}_${sceneId}`,
                    suiteName: curTask?.planName || '未知任务',
                    caseName: selectedCase?.caseNodeName || '未知用例',
                    sceneName: selectedScene?.name || '未知场景',
                    avg: reportData.avg,
                    max: reportData.max,
                    min: reportData.min,
                    rowSpan: 1 // 表格合并行用
                };

                newReportDataArray[modalIndex] = processedData;
                newReportTableDataList[modalIndex] = [tableDataItem]; // 表格数据
                newReportInfoList[modalIndex] = [{ caseId, sceneId }];

                setReportDataArray(newReportDataArray);
                setReportTableDataList(newReportTableDataList);
                setReportInfoList(newReportInfoList);

                message.success('数据添加成功');
            } else {
                message.error(response?.msg || '获取报告数据失败');
            }
        } catch (error) {
            console.error('获取报告数据失败:', error);
            message.error('获取报告数据失败');
        } finally {
            setLoading(false);
        }
    }, [reportTaskOption, reportDataArray, reportTableDataList, reportInfoList, curTask, sceneList]);

    return (
        <Layout>
            <Content
                style={{
                    backgroundColor: '#fff',
                    minHeight: 'calc(100vh - 160px)',
                    overflow: 'auto',
                    padding: '0 25px 20px'
                }}
            >
                {/* 报告标题设置 */}
                <InfoTitle
                    reportTitle={reportTitle}
                    onTitleChange={setReportTitle}
                    onAddModule={addNewModule}
                />

                {/* 筛选器 - 只有当有报告模块时才显示 */}
                {reportNames.length > 0 && (
                    <div>
                        <InfoFilter
                            curTask={curTask}
                            sceneList={sceneList}
                            reportTaskOption={reportTaskOption}
                            onOptionChange={setReportTaskOption}
                            reportNames={reportNames}
                            onAddData={addDataToReport}
                            loading={loading}
                        />
                        <Divider style={{ margin: 0, padding: 0 }} />
                    </div>
                )}

                <br />

                {/* 报告内容展示 */}
                <ReportOutput
                    reportTitle={reportTitle}
                    reportNames={reportNames}
                    reportDataArray={reportDataArray}
                    reportDescList={reportDescList}
                    reportTableDataList={reportTableDataList}
                    onUpdateTitle={(index, title) => {
                        const newNames = [...reportNames];
                        newNames[index] = title;
                        setReportNames(newNames);
                    }}
                    onUpdateDescription={(index, desc) => {
                        const newDescList = [...reportDescList];
                        newDescList[index] = desc;
                        setReportDescList(newDescList);
                    }}
                    onDeleteModule={(index) => {
                        setReportNames(reportNames.filter((_, i) => i !== index));
                        setReportDataArray(reportDataArray.filter((_, i) => i !== index));
                        setReportDescList(reportDescList.filter((_, i) => i !== index));
                        setReportTableDataList(reportTableDataList.filter((_, i) => i !== index));
                        setReportInfoList(reportInfoList.filter((_, i) => i !== index));
                        message.success('模块删除成功');
                    }}
                    onMoveUp={moveModuleUp}
                    onMoveDown={moveModuleDown}
                />
                
            </Content>
        </Layout>
    );
};

// InfoTitle 组件 - 报告标题和添加新模块
const InfoTitle = ({ reportTitle, onTitleChange, onAddModule }) => {
    const [form] = Form.useForm();

    return (
        <Form layout="inline" form={form} style={{ paddingBottom: 15 }}>
            <Row style={{ width: '100%' }}>
                <Col span={7}>
                    <Form.Item label="报告名称" name="reportTitle">
                        <Input
                            placeholder="请输入报告名称"
                            style={{ width: '100%' }}
                            value={reportTitle}
                            onChange={(e) => onTitleChange(e.target.value)}
                        />
                    </Form.Item>
                </Col>
                <Col span={3}>
                    <Form.Item>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={onAddModule}
                        >
                            添加新模块
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    );
};

// InfoFilter 组件 - 筛选器
const InfoFilter = ({ curTask, sceneList, reportTaskOption, onOptionChange, reportNames, onAddData, loading }) => {
    // 构建用例选项（参考 FramePage 的实现）
    const caseOptions = (curTask?.planParams?.caseNodeParams?.caseNodeList || []).map(
        (item) => (
            <Option key={item?.caseNodeId} value={item?.caseNodeId}>
                {item?.caseNodeName}
            </Option>
        )
    );

    // 构建场景选项
    const sceneOptions = (sceneList || []).map((item) => (
        <Option key={item?.id} value={item?.id}>
            {item?.name}
        </Option>
    ));

    // 构建模块选项
    const moduleOptions = reportNames.map((name, index) => (
        <Option key={`modal_index_${index}`} value={index}>
            {name}
        </Option>
    ));

    return (
        <Form
            style={{
                width: '100%',
                paddingTop: '16px',
                borderTop: '1px solid #f5f5f5'
            }}
        >
            <Row>
                <Col span={6}>
                    <Form.Item label="执行用例" required style={{ width: '100%' }}>
                        <Select
                            value={reportTaskOption.caseId}
                            placeholder="请选择执行用例"
                            onChange={(value) => {
                                const newOption = {
                                    ...reportTaskOption,
                                    caseId: value,
                                    sceneId: null // 不默认选中第一个场景
                                };
                                onOptionChange(newOption);
                            }}
                        >
                            {caseOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={6} offset={1}>
                    <Form.Item label="执行场景" required style={{ width: '100%' }}>
                        <Select
                            value={reportTaskOption.sceneId}
                            placeholder="请选择执行场景"
                            onChange={(value) => {
                                const newOption = {
                                    ...reportTaskOption,
                                    sceneId: value
                                };
                                onOptionChange(newOption);
                            }}
                        >
                            {sceneOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={6} offset={1}>
                    <Form.Item label="报告模块" required style={{ width: '100%' }}>
                        <Select
                            value={reportTaskOption.modalIndex}
                            placeholder="请选择报告模块"
                            onChange={(value) => {
                                const newOption = {
                                    ...reportTaskOption,
                                    modalIndex: value
                                };
                                onOptionChange(newOption);
                            }}
                        >
                            {moduleOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={3}>
                    <Form.Item>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={onAddData}
                        >
                            添加至报告
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    );
};

// ReportOutput 组件 - 报告输出
const ReportOutput = ({
    reportTitle,
    reportNames,
    reportDataArray,
    reportDescList,
    reportTableDataList,
    onUpdateTitle,
    onUpdateDescription,
    onDeleteModule,
    onMoveUp,
    onMoveDown
}) => {
    const [downloadLoading, setDownloadLoading] = useState(false);

    // 下载报告
    const downloadReport = async () => {
        const element = document.querySelector('.report-container');
        if (!element) {
            message.error('报告内容未找到');
            return;
        }
        setDownloadLoading(true);
        try {
            const canvas = await html2canvas(element, {
                useCORS: true,
                scale: 2,
                backgroundColor: '#ffffff'
            });

            const image = canvas.toDataURL('image/jpeg', 1.0);
            const link = document.createElement('a');
            link.href = image;
            link.download = `${reportTitle || '性能评测报告'}.jpg`;
            link.click();
        } finally {
            setDownloadLoading(false);
        }
    };

    const showDownloadButton = reportTitle || reportNames.length > 0;

    return (
        <div>
            <div className="report-container" style={{ padding: 20 }}>
                {/* 报告标题 */}
                {reportTitle && (
                    <div style={{ paddingTop: 20 }}>
                        <Title level={1} style={{ textAlign: 'center', fontSize: 26 }}>
                            {reportTitle}
                        </Title>
                        <Divider style={{ margin: 0, padding: 0 }} />
                    </div>
                )}

                {/* 报告模块列表 */}
                {reportNames.map((name, index) => (
                    <div key={index}>
                        <ReportModule
                            name={name}
                            index={index}
                            description={reportDescList[index] || ''}
                            reportData={reportDataArray[index]}
                            reportTableData={reportTableDataList[index]}
                            onUpdateTitle={onUpdateTitle}
                            onUpdateDescription={onUpdateDescription}
                            onDelete={onDeleteModule}
                            onMoveUp={onMoveUp}
                            onMoveDown={onMoveDown}
                        />
                        {index < reportNames.length - 1 && <Divider />}
                    </div>
                ))}

                {/* 联系我们 */}
                {/* {showDownloadButton && (
                    <div style={{ paddingTop: 20, color: '#777' }}>
                        <Text style={{ textAlign: 'center', fontWeight: 'bold', display: 'block' }}>
                            百度移动端性能评测中心 | LazyPerf 提供技术支持
                        </Text>
                    </div>
                )} */}
            </div>

            {/* 下载按钮 */}
            {showDownloadButton && (
                <div
                    style={{
                        width: '100%',
                        position: 'relative',
                        marginTop: 10
                    }}
                >
                    <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        loading={downloadLoading}
                        style={{
                            position: 'absolute',
                            top: 30,
                            left: '50%',
                            transform: 'translateX(-50%)'
                        }}
                        onClick={downloadReport}
                    >
                        {downloadLoading ? '下载中...' : '下载报告'}
                    </Button>
                </div>
            )}
        </div>
    );
};

// ReportModule 组件 - 单个报告模块
const ReportModule = ({
    name,
    index,
    description,
    reportData,
    reportTableData,
    onUpdateTitle,
    onUpdateDescription,
    onDelete,
    onMoveUp,
    onMoveDown
}) => {
    return (
        <div style={{ position: 'relative', paddingTop: 10 }}>
            {/* 操作按钮 - 按照旧版本样式 */}
            <Tooltip placement="left" title='上移'>
                <UpCircleTwoTone
                    style={{
                        position: 'absolute',
                        right: 40,
                        top: 10
                    }}
                    onClick={() => onMoveUp(index)}
                />
            </Tooltip>
            <Tooltip placement="left" title='下移'>
                <DownCircleTwoTone
                    style={{
                        position: 'absolute',
                        right: 20,
                        top: 10
                    }}
                    onClick={() => onMoveDown(index)}
                />
            </Tooltip>
            <Popconfirm
                placement="left"
                title="确认删除该模块评测报告吗?"
                onConfirm={() => onDelete(index)}
                okText="确定"
                cancelText="取消"
            >
                <DeleteTwoTone
                    twoToneColor='#ff4d4f'
                    style={{
                        position: 'absolute',
                        right: 0,
                        top: 10
                    }}
                />
            </Popconfirm>

            {/* 模块标题 */}
            <div>
                <Input
                    value={name}
                    style={{
                        border: 'none',
                        paddingLeft: 0,
                        width: '50%',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }}
                    onChange={(e) => onUpdateTitle(index, e.target.value)}
                />
            </div>

            {/* 结论描述 */}
            <Card size="small" title="结论描述" style={{ marginTop: 15, width: '100%' }}>
                <TextArea
                    allowClear
                    style={{
                        height: '100%',
                        width: '95%',
                        margin: '10px 0',
                        marginLeft: '2.5%'
                    }}
                    value={description}
                    onChange={(e) => onUpdateDescription(index, e.target.value)}
                />
            </Card>

            {/* 数据展示 */}
            {reportData && (
                <ReportDataDisplay reportData={reportData} reportTableData={reportTableData} />
            )}
        </div>
    );
};

// ReportDataDisplay 组件 - 报告数据展示（按照旧版本样式）
const ReportDataDisplay = ({ reportData, reportTableData }) => {
    // 如果没有数据，不显示任何内容
    if (!reportData || !reportTableData || reportTableData.length === 0) {
        return null;
    }

    // 数据表格的列定义（按照旧版本）
    const columns = [
        {
            title: '目录',
            width: 250,
            dataIndex: 'suiteName',
            key: 'suiteName',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            }
        },
        {
            title: '用例',
            width: 250,
            dataIndex: 'caseName',
            key: 'caseName',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            }
        },
        {
            title: '场景',
            width: 250,
            dataIndex: 'sceneName',
            key: 'sceneName',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            }
        },
        {
            title: '均值',
            width: 250,
            dataIndex: 'avg',
            key: 'avg',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            },
            render: (text) => {
                return (
                    <span>{text} ms</span>
                )
            }
        },
        {
            title: '最大值',
            width: 250,
            dataIndex: 'max',
            key: 'max',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            },
            render: (text) => {
                return (
                    <span>{text} ms</span>
                )
            }
        },
        {
            title: '最小值',
            width: 250,
            dataIndex: 'min',
            key: 'min',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            },
            render: (text) => {
                return (
                    <span>{text} ms</span>
                )
            }
        }
    ];

    return (
        <div>
            {/* 数据表格 */}
            <Card size="small" title='数据表' style={{ marginTop: 15 }}>
                <Table
                    style={{ margin: 10 }}
                    columns={columns}
                    pagination={false}
                    dataSource={reportTableData}
                />
            </Card>

            {/* 盒须图 */}
            <ReportChart reportData={reportData} />
        </div>
    );
};

// ReportChart 组件 - 盒须图（按照旧版本样式）
const ReportChart = ({ reportData }) => {
    // 由于新API返回的数据结构简化了，我们需要构造图表数据
    // 这里先显示一个简化版本，如果需要完整的盒须图，需要更多的数据处理
    const { min, max, avg, confidence, totalCount, actualCount } = reportData;

    // 计算置信区间
    const leftConfidence = avg - 2 * confidence;
    const rightConfidence = avg + 2 * confidence;

    // 构造简化的图表数据
    const chartOption = {
        grid: {
            left: '3%',
            right: '4%',
            bottom: '5%',
            containLabel: true
        },
        legend: {
            show: true,
            data: [{
                name: '置信区间',
                icon: 'rect'
            }],
            x: 'right'
        },
        xAxis: {
            type: 'value',
            min: min,
            max: max
        },
        yAxis: [{
            type: 'category',
            data: ['数据分布']
        }, {
            type: 'category',
            data: [`样本数: ${actualCount}/${totalCount}`]
        }],
        series: [
            {
                name: 'Placeholder',
                type: 'bar',
                stack: 'total',
                itemStyle: {
                    borderColor: 'transparent',
                    color: 'transparent'
                },
                emphasis: {
                    itemStyle: {
                        borderColor: 'transparent',
                        color: 'transparent',
                        focus: 'series'
                    }
                },
                data: [min]
            },
            {
                name: 'left',
                type: 'effectScatter',
                barGap: '-100%',
                smooth: true,
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        textStyle: { color: '#000' },
                        formatter: function (v) {
                            return '最小值:' + v.value;
                        }
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(128, 128, 128, 0)',
                        borderWidth: 0,
                        borderColor: '#1FBCD2'
                    }
                },
                symbolOffset: [0, -15],
                data: [min]
            },
            {
                name: 'minToLeft',
                type: 'bar',
                stack: 'total',
                barWidth: 30,
                color: 'Silver',
                emphasis: {
                    focus: 'series'
                },
                data: [leftConfidence - min]
            },
            {
                name: '置信区间',
                type: 'bar',
                stack: 'total',
                color: 'CornflowerBlue',
                emphasis: {
                    focus: 'series'
                },
                data: [rightConfidence - leftConfidence]
            },
            {
                name: 'avg',
                type: 'effectScatter',
                barGap: '-100%',
                smooth: true,
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        textStyle: { color: '#000' },
                        formatter: function (v) {
                            return '平均值:' + v.value;
                        }
                    }
                },
                itemStyle: {
                    color: 'rgba(115, 159, 250, .5)',
                },
                symbol: 'pin',
                symbolSize: [25, 25],
                symbolOffset: [0, -15],
                data: [avg]
            },
            {
                name: 'rightToMax',
                type: 'bar',
                stack: 'total',
                color: 'Silver',
                emphasis: {
                    focus: 'series'
                },
                data: [max - rightConfidence]
            },
            {
                name: 'max',
                type: 'effectScatter',
                barGap: '-100%',
                smooth: true,
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        textStyle: { color: '#000' },
                        formatter: function (v) {
                            return '最大值：' + (v.value);
                        }
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(128, 128, 128, 0)',
                        borderWidth: 0,
                        borderColor: '#1FBCD2'
                    }
                },
                symbolOffset: [0, -15],
                data: [max]
            }
        ]
    };

    return (
        <Card size="small" title='盒须图' style={{ marginTop: 15 }}>
            <ReactECharts
                option={chartOption}
                style={{ height: 200, margin: 10 }}
            />
        </Card>
    );
};

export default ReportPage;
export { ReportPage };