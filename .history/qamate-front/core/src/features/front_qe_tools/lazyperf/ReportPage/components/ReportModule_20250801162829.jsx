
import { Input, Card, Tooltip, Popconfirm } from 'antd';
import { UpCircleTwoTone, DownCircleTwoTone, DeleteTwoTone } from '@ant-design/icons';
import ReportDataDisplay from './ReportDataDisplay';

const { TextArea } = Input;

const ReportModule = ({
    name,
    index,
    description,
    reportData,
    reportTableData,
    onUpdateTitle,
    onUpdateDescription,
    onDelete,
    onMoveUp,
    onMoveDown
}) => {
    return (
        <div style={{ position: 'relative', paddingTop: 10 }}>
            <Tooltip placement="left" title='上移'>
                <UpCircleTwoTone
                    style={{
                        position: 'absolute',
                        right: 40,
                        top: 10
                    }}
                    onClick={() => onMoveUp(index)}
                />
            </Tooltip>
            <Tooltip placement="left" title='下移'>
                <DownCircleTwoTone
                    style={{
                        position: 'absolute',
                        right: 20,
                        top: 10
                    }}
                    onClick={() => onMoveDown(index)}
                />
            </Tooltip>
            <Popconfirm
                placement="left"
                title="确认删除该模块评测报告吗?"
                onConfirm={() => onDelete(index)}
                okText="确定"
                cancelText="取消"
            >
                <DeleteTwoTone
                    twoToneColor='#ff4d4f'
                    style={{
                        position: 'absolute',
                        right: 0,
                        top: 10
                    }}
                />
            </Popconfirm>

            {/* 模块标题 */}
            <div>
                <Input
                    value={name}
                    style={{
                        border: 'none',
                        paddingLeft: 0,
                        width: '50%',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }}
                    onChange={(e) => onUpdateTitle(index, e.target.value)}
                />
            </div>

            {/* 结论描述 */}
            <Card size="small" title="结论描述" style={{ marginTop: 15, width: '100%' }}>
                <TextArea
                    allowClear
                    style={{
                        height: '100%',
                        width: '95%',
                        margin: '10px 0',
                        marginLeft: '2.5%'
                    }}
                    value={description}
                    onChange={(e) => onUpdateDescription(index, e.target.value)}
                />
            </Card>

            {/* 数据展示 */}
            {reportData && (
                <ReportDataDisplay reportData={reportData} reportTableData={reportTableData} />
            )}
        </div>
    );
};

export default ReportModule;
