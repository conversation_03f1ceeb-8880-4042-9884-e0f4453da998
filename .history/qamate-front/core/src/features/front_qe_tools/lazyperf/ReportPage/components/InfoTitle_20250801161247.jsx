/**
 * @file InfoTitle.jsx 报告标题和添加新模块组件
 * @create 2025.08.01
 */
import React from 'react';
import { Form, Input, Button, Row, Col } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const InfoTitle = ({ reportTitle, onTitleChange, onAddModule }) => {
    const [form] = Form.useForm();

    return (
        <Form layout="inline" form={form} style={{ paddingBottom: 15 }}>
            <Row style={{ width: '100%' }}>
                <Col span={7}>
                    <Form.Item label="报告名称" name="reportTitle">
                        <Input
                            placeholder="请输入报告名称"
                            style={{ width: '100%' }}
                            value={reportTitle}
                            onChange={(e) => onTitleChange(e.target.value)}
                        />
                    </Form.Item>
                </Col>
                <Col span={3}>
                    <Form.Item>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={onAddModule}
                        >
                            添加新模块
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    );
};

export default InfoTitle;
