/**
 * @file ReportChart.jsx 盒须图组件
 * @create 2025.08.01
 */
import React from 'react';
import { Card } from 'antd';
import ReactECharts from 'echarts-for-react';

const ReportChart = ({ reportData }) => {
    // 由于新API返回的数据结构简化了，我们需要构造图表数据
    // 这里先显示一个简化版本，如果需要完整的盒须图，需要更多的数据处理
    const { min, max, avg, confidence, totalCount, actualCount } = reportData;

    // 计算置信区间
    const leftConfidence = avg - 2 * confidence;
    const rightConfidence = avg + 2 * confidence;

    // 构造简化的图表数据
    const chartOption = {
        grid: {
            left: '3%',
            right: '4%',
            bottom: '5%',
            containLabel: true
        },
        legend: {
            show: true,
            data: [{
                name: '置信区间',
                icon: 'rect'
            }],
            x: 'right'
        },
        xAxis: {
            type: 'value',
            min: min,
            max: max
        },
        yAxis: [{
            type: 'category',
            data: ['数据分布']
        }, {
            type: 'category',
            data: [`样本数: ${actualCount}/${totalCount}`]
        }],
        series: [
            {
                name: 'Placeholder',
                type: 'bar',
                stack: 'total',
                itemStyle: {
                    borderColor: 'transparent',
                    color: 'transparent'
                },
                emphasis: {
                    itemStyle: {
                        borderColor: 'transparent',
                        color: 'transparent',
                        focus: 'series'
                    }
                },
                data: [min]
            },
            {
                name: 'left',
                type: 'effectScatter',
                barGap: '-100%',
                smooth: true,
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        textStyle: { color: '#000' },
                        formatter: function (v) {
                            return '最小值:' + v.value;
                        }
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(128, 128, 128, 0)',
                        borderWidth: 0,
                        borderColor: '#1FBCD2'
                    }
                },
                symbolOffset: [0, -15],
                data: [min]
            },
            {
                name: 'minToLeft',
                type: 'bar',
                stack: 'total',
                barWidth: 30,
                color: 'Silver',
                emphasis: {
                    focus: 'series'
                },
                data: [leftConfidence - min]
            },
            {
                name: '置信区间',
                type: 'bar',
                stack: 'total',
                color: 'CornflowerBlue',
                emphasis: {
                    focus: 'series'
                },
                data: [rightConfidence - leftConfidence]
            },
            {
                name: 'avg',
                type: 'effectScatter',
                barGap: '-100%',
                smooth: true,
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        textStyle: { color: '#000' },
                        formatter: function (v) {
                            return '平均值:' + v.value;
                        }
                    }
                },
                itemStyle: {
                    color: 'rgba(115, 159, 250, .5)',
                },
                symbol: 'pin',
                symbolSize: [25, 25],
                symbolOffset: [0, -15],
                data: [avg]
            },
            {
                name: 'rightToMax',
                type: 'bar',
                stack: 'total',
                color: 'Silver',
                emphasis: {
                    focus: 'series'
                },
                data: [max - rightConfidence]
            },
            {
                name: 'max',
                type: 'effectScatter',
                barGap: '-100%',
                smooth: true,
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        textStyle: { color: '#000' },
                        formatter: function (v) {
                            return '最大值：' + (v.value);
                        }
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(128, 128, 128, 0)',
                        borderWidth: 0,
                        borderColor: '#1FBCD2'
                    }
                },
                symbolOffset: [0, -15],
                data: [max]
            }
        ]
    };

    return (
        <Card size="small" title='盒须图' style={{ marginTop: 15 }}>
            <ReactECharts
                option={chartOption}
                style={{ height: 200, margin: 10 }}
            />
        </Card>
    );
};

export default ReportChart;
