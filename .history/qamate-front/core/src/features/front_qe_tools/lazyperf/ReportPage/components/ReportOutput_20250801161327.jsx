/**
 * @file ReportOutput.jsx 报告输出组件
 * @create 2025.08.01
 */
import React, { useState } from 'react';
import { Button, Typography, Divider, message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import html2canvas from 'html2canvas';
import ReportModule from './ReportModule';

const { Title } = Typography;

const ReportOutput = ({
    reportTitle,
    reportNames,
    reportDataArray,
    reportDescList,
    reportTableDataList,
    onUpdateTitle,
    onUpdateDescription,
    onDeleteModule,
    onMoveUp,
    onMoveDown
}) => {
    const [downloadLoading, setDownloadLoading] = useState(false);

    // 下载报告
    const downloadReport = async () => {
        const element = document.querySelector('.report-container');
        if (!element) {
            message.error('报告内容未找到');
            return;
        }
        setDownloadLoading(true);
        try {
            const canvas = await html2canvas(element, {
                useCORS: true,
                scale: 2,
                backgroundColor: '#ffffff'
            });

            const image = canvas.toDataURL('image/jpeg', 1.0);
            const link = document.createElement('a');
            link.href = image;
            link.download = `${reportTitle || '性能评测报告'}.jpg`;
            link.click();
        } finally {
            setDownloadLoading(false);
        }
    };

    const showDownloadButton = reportTitle || reportNames.length > 0;

    return (
        <div>
            <div className="report-container" style={{ padding: 20 }}>
                {/* 报告标题 */}
                {reportTitle && (
                    <div style={{ paddingTop: 20 }}>
                        <Title level={1} style={{ textAlign: 'center', fontSize: 26 }}>
                            {reportTitle}
                        </Title>
                        <Divider style={{ margin: 0, padding: 0 }} />
                    </div>
                )}

                {/* 报告模块列表 */}
                {reportNames.map((name, index) => (
                    <div key={index}>
                        <ReportModule
                            name={name}
                            index={index}
                            description={reportDescList[index] || ''}
                            reportData={reportDataArray[index]}
                            reportTableData={reportTableDataList[index]}
                            onUpdateTitle={onUpdateTitle}
                            onUpdateDescription={onUpdateDescription}
                            onDelete={onDeleteModule}
                            onMoveUp={onMoveUp}
                            onMoveDown={onMoveDown}
                        />
                        {index < reportNames.length - 1 && <Divider />}
                    </div>
                ))}

                {/* 联系我们 */}
                {/* {showDownloadButton && (
                    <div style={{ paddingTop: 20, color: '#777' }}>
                        <Text style={{ textAlign: 'center', fontWeight: 'bold', display: 'block' }}>
                            百度移动端性能评测中心 | LazyPerf 提供技术支持
                        </Text>
                    </div>
                )} */}
            </div>

            {/* 下载按钮 */}
            {showDownloadButton && (
                <div
                    style={{
                        width: '100%',
                        position: 'relative',
                        marginTop: 10
                    }}
                >
                    <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        loading={downloadLoading}
                        style={{
                            position: 'absolute',
                            top: 30,
                            left: '50%',
                            transform: 'translateX(-50%)'
                        }}
                        onClick={downloadReport}
                    >
                        {downloadLoading ? '下载中...' : '下载报告'}
                    </Button>
                </div>
            )}
        </div>
    );
};

export default ReportOutput;
