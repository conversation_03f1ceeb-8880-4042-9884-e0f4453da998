/**
 * @file ReportDataDisplay.jsx 报告数据展示组件
 * @create 2025.08.01
 */
import React from 'react';
import { Card, Table } from 'antd';
import ReportChart from './ReportChart';

const ReportDataDisplay = ({ reportData, reportTableData }) => {
    if (!reportData || !reportTableData || reportTableData.length === 0) {
        return null;
    }

    // 数据表格的列定义
    const columns = [
        {
            title: '目录',
            width: 250,
            dataIndex: 'suiteName',
            key: 'suiteName',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            }
        },
        {
            title: '用例',
            width: 250,
            dataIndex: 'caseName',
            key: 'caseName',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            }
        },
        {
            title: '场景',
            width: 250,
            dataIndex: 'sceneName',
            key: 'sceneName',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            }
        },
        {
            title: '均值',
            width: 250,
            dataIndex: 'avg',
            key: 'avg',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            },
            render: (text) => {
                return (
                    <span>{text} ms</span>
                )
            }
        },
        {
            title: '最大值',
            width: 250,
            dataIndex: 'max',
            key: 'max',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            },
            render: (text) => {
                return (
                    <span>{text} ms</span>
                )
            }
        },
        {
            title: '最小值',
            width: 250,
            dataIndex: 'min',
            key: 'min',
            align: 'center',
            onCell: (record) => {
                return { rowSpan: record.rowSpan };
            },
            render: (text) => {
                return (
                    <span>{text} ms</span>
                )
            }
        }
    ];

    return (
        <div>
            {/* 数据表格 */}
            <Card size="small" title='数据表' style={{ marginTop: 15 }}>
                <Table
                    style={{ margin: 10 }}
                    columns={columns}
                    pagination={false}
                    dataSource={reportTableData}
                />
            </Card>

            {/* 盒须图 */}
            <ReportChart reportData={reportData} />
        </div>
    );
};

export default ReportDataDisplay;
