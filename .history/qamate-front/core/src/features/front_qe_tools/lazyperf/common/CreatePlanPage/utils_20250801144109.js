import { Tag } from 'antd';
import React from 'react';
import styles from './CreatePlanPage.module.less';
import { v4 as uuidv4 } from 'uuid';
import { isEmpty } from 'lodash';

// 策略配置选项
export const STRATEGY_OPTIONS = {
    recordStart: [
        {
            value: 'app_start',
            label: 'App启动',
            desc: '从App启动开始录制',
            color: '#52c41a',
            order: 1
        },
        {
            value: 'activity_start',
            label: 'Activity启动',
            desc: '从Activity启动开始录制',
            color: '#1890ff',
            order: 2
        },
        {
            value: 'page_start',
            label: '页面启动',
            desc: '从页面启动开始录制',
            color: '#722ed1',
            order: 3
        }
    ],
    firstFrame: [
        {
            value: 'algorithm_first_frame',
            label: '算法校准',
            desc: '使用算法自动识别首帧',
            color: '#fa8c16',
            order: 0
        },
        {
            value: 'app_first_frame',
            label: 'App首帧',
            desc: 'App完全启动后的首帧',
            color: '#52c41a',
            order: 1
        },
        {
            value: 'activity_first_frame',
            label: 'Activity首帧',
            desc: 'Activity完全显示后的首帧',
            color: '#1890ff',
            order: 2
        },
        {
            value: 'page_first_frame',
            label: '页面首帧',
            desc: '页面完全加载后的首帧',
            color: '#722ed1',
            order: 3
        }
    ],
    lastFrame: [
        {
            value: 'algorithm_last_frame',
            label: '算法校准',
            desc: '使用算法自动识别尾帧',
            color: '#eb2f96',
            order: -1
        }
    ]
};

/**
 * 获取策略的执行顺序
 * @param {string} strategy - 策略值
 * @returns {number} 策略的执行顺序
 */
export const getStrategyOrder = (strategy) => {
    // 查找策略在配置中的顺序
    const allStrategies = [
        ...STRATEGY_OPTIONS.recordStart,
        ...STRATEGY_OPTIONS.firstFrame,
        ...STRATEGY_OPTIONS.lastFrame
    ];
    const strategyConfig = allStrategies.find((item) => item.value === strategy);
    return strategyConfig ? strategyConfig.order : 999;
};

/**
 * 验证策略选择是否合理（首帧应该在录制起始之后或同时，尾帧应该在首帧之后或同时）
 * @param {string} recordStart - 录制起始策略
 * @param {string} firstFrame - 首帧策略
 * @param {string} lastFrame - 尾帧策略
 * @returns {boolean} 是否合理
 */
export const validateStrategyOrder = (recordStart, firstFrame, lastFrame) => {
    const recordOrder = getStrategyOrder(recordStart);
    const firstOrder = getStrategyOrder(firstFrame);
    const lastOrder = getStrategyOrder(lastFrame);

    // 算法校准特殊处理
    if (firstFrame && firstFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以与任何录制起始配合
    }

    if (lastFrame && lastFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以作为任何尾帧
    }

    // 机制校准需要满足顺序关系
    if (recordStart && firstFrame && recordOrder > firstOrder) {
        return false;
    }

    if (firstFrame && lastFrame && firstOrder > lastOrder) {
        return false;
    }

    return true;
};

/**
 * 根据录制起始策略过滤可用的首帧策略
 * @param {string} recordStart - 录制起始策略
 * @returns {Array} 可用的首帧策略选项
 */
export const getAvailableFirstFrameOptions = (recordStart) => {
    if (!recordStart) {
        return STRATEGY_OPTIONS.firstFrame;
    }

    const recordOrder = getStrategyOrder(recordStart);
    return STRATEGY_OPTIONS.firstFrame.filter((option) => {
        // 算法校准
        if (option.value.startsWith('algorithm_')) {
            return true;
        }

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= recordOrder;
    });
};

/**
 * 根据首帧策略过滤可用的尾帧策略
 * @param {string} firstFrameStrategy - 首帧策略
 * @returns {Array} 可用的尾帧策略选项
 */
export const getAvailableLastFrameOptions = (firstFrameStrategy) => {
    if (!firstFrameStrategy) return STRATEGY_OPTIONS.lastFrame;

    // 算法校准的首帧可以配合任何尾帧
    if (firstFrameStrategy.startsWith('algorithm_')) {
        return STRATEGY_OPTIONS.lastFrame;
    }

    const firstOrder = getStrategyOrder(firstFrameStrategy);
    return STRATEGY_OPTIONS.lastFrame.filter((option) => {
        // 算法校准
        if (option.value.startsWith('algorithm_')) {
            return true;
        }

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= firstOrder;
    });
};

/**
 * 获取可用的录制起始选项（基于步骤信息）
 * @param {Array} stepInfo - 步骤信息数组
 * @returns {Array} 录制起始选项
 */
export const getRecordStartOptions = (stepInfo) => {
    const options = [...STRATEGY_OPTIONS.recordStart];

    if (stepInfo && stepInfo.length > 0) {
        stepInfo.forEach((step) => {
            options.push({
                value: step.value,
                label: (
                    <div className={styles.flexCenter}>
                        <Tag color="blue">{step.category}</Tag>
                        <span>{step.label}</span>
                    </div>
                )
                // desc: step.desc
            });
        });
    }

    return options;
};

/**
 * 获取可用的首帧界定选项（基于步骤信息）
 * @param {Array} stepInfo - 步骤信息数组
 * @returns {Array} 首帧界定选项
 */
export const getFirstFrameOptionsFromSteps = (stepInfo) => {
    const options = [];

    if (stepInfo && stepInfo.length > 0) {
        stepInfo.forEach((step) => {
            options.push({
                value: step.value,
                label: (
                    <div className={styles.flexCenter}>
                        <Tag color="blue">{step.category}</Tag>
                        <span>{step.label}</span>
                    </div>
                ),
                desc: step.desc
            });
        });
    }

    return options;
};

/**
 * 获取可用的尾帧界定选项（基于步骤信息）
 * @param {Array} stepInfo - 步骤信息数组
 * @returns {Array} 尾帧界定选项
 */
export const getLastFrameOptionsFromSteps = (stepInfo) => {
    const options = [];

    if (stepInfo && stepInfo.length > 0) {
        stepInfo.forEach((step) => {
            options.push({
                value: step.value.replace('_start', '_end'),
                label: (
                    <div className={styles.flexCenter}>
                        <Tag color="volcano">{step.category}</Tag>
                        <span>{step.label}</span>
                    </div>
                ),
                desc: step.desc + ' 结束'
            });
        });
    }

    return options;
};