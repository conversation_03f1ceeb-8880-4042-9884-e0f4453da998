import { Tag } from 'antd';
import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { isEmpty } from 'lodash';
import { STRATEGY_OPTIONS } from './const.js';
import styles from './CreatePlanPage.module.less';

//  获取策略的执行顺序
export const getStrategyOrder = (strategy) => {
    // 查找策略在配置中的顺序
    const allStrategies = [
        ...STRATEGY_OPTIONS.recordStart,
        ...STRATEGY_OPTIONS.firstFrame,
        ...STRATEGY_OPTIONS.lastFrame
    ];
    const strategyConfig = allStrategies.find((item) => item.value === strategy);
    return strategyConfig ? strategyConfig.order : 999;
};

// 验证策略选择是否合理（首帧应该在录制起始之后或同时，尾帧应该在首帧之后或同时）
export const validateStrategyOrder = (recordStart, firstFrame, lastFrame) => {
    const recordOrder = getStrategyOrder(recordStart);
    const firstOrder = getStrategyOrder(firstFrame);
    const lastOrder = getStrategyOrder(lastFrame);

    // 算法校准特殊处理
    if (firstFrame && firstFrame.startsWith('algorithm_')) {
        return true;
    }

    if (lastFrame && lastFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以作为任何尾帧
    }

    // 机制校准需要满足顺序关系
    if (recordStart && firstFrame && recordOrder > firstOrder) {
        return false;
    }

    if (firstFrame && lastFrame && firstOrder > lastOrder) {
        return false;
    }

    return true;
};

//  根据录制起始策略过滤可用的首帧策略
export const getAvailableFirstFrameOptions = (recordStart) => {
    if (!recordStart) {
        return STRATEGY_OPTIONS.firstFrame;
    }

    const recordOrder = getStrategyOrder(recordStart);
    return STRATEGY_OPTIONS.firstFrame.filter((option) => {
        // 算法校准
        if (option.value.startsWith('algorithm_')) {
            return true;
        }

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= recordOrder;
    });
};

/**
 * 根据首帧策略过滤可用的尾帧策略
 * @param {string} firstFrameStrategy - 首帧策略
 * @returns {Array} 可用的尾帧策略选项
 */
export const getAvailableLastFrameOptions = (firstFrameStrategy) => {
    if (!firstFrameStrategy) return STRATEGY_OPTIONS.lastFrame;

    // 算法校准的首帧可以配合任何尾帧
    if (firstFrameStrategy.startsWith('algorithm_')) {
        return STRATEGY_OPTIONS.lastFrame;
    }

    const firstOrder = getStrategyOrder(firstFrameStrategy);
    return STRATEGY_OPTIONS.lastFrame.filter((option) => {
        // 算法校准
        if (option.value.startsWith('algorithm_')) {
            return true;
        }

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= firstOrder;
    });
};

/**
 * 通用的步骤选项生成函数
 * @param {Array} stepInfo - 步骤信息数组
 * @param {Object} config - 配置选项
 * @param {Array} config.baseOptions - 基础选项（可选）
 * @param {Function} config.valueTransform - 值转换函数（可选）
 * @param {string} config.descSuffix - 描述后缀（可选）
 * @param {boolean} config.includeDesc - 是否包含描述（默认false）
 * @returns {Array} 选项数组
 */
export const generateStepOptions = (stepInfo, config = {}) => {
    const { baseOptions = [], valueTransform = (value) => value, descSuffix = '', includeDesc = false } = config;

    const options = [...baseOptions];

    if (stepInfo && stepInfo.length > 0) {
        stepInfo.forEach((step) => {
            const option = {
                value: valueTransform(step.value),
                label: (
                    <div className={styles.flexCenter}>
                        <Tag color="blue">{step.category}</Tag>
                        <span>{step.label}</span>
                    </div>
                )
            };

            if (includeDesc) {
                option.desc = step.desc + descSuffix;
            }

            options.push(option);
        });
    }

    return options;
};

/**
 * 获取可用的录制起始选项（基于步骤信息）
 * @param {Array} stepInfo - 步骤信息数组
 * @returns {Array} 录制起始选项
 */
export const getRecordStartOptions = (stepInfo) => {
    return generateStepOptions(stepInfo, {
        baseOptions: STRATEGY_OPTIONS.recordStart
    });
};

/**
 * 获取可用的首帧界定选项（基于步骤信息）
 * @param {Array} stepInfo - 步骤信息数组
 * @returns {Array} 首帧界定选项
 */
export const getFirstFrameOptionsFromSteps = (stepInfo) => {
    return generateStepOptions(stepInfo, {
        includeDesc: true
    });
};

/**
 * 获取可用的尾帧界定选项（基于步骤信息）
 * @param {Array} stepInfo - 步骤信息数组
 * @returns {Array} 尾帧界定选项
 */
export const getLastFrameOptionsFromSteps = (stepInfo) => {
    return generateStepOptions(stepInfo, {
        valueTransform: (value) => value.replace('_start', '_end'),
        descSuffix: ' 结束',
        includeDesc: true
    });
};

/**
 * 从树形数据中获取节点的完整路径名称
 * @param {Array} treeData - 树形数据
 * @param {string|number} nodeId - 节点ID
 * @returns {string} 节点路径
 */
export const getNodePathFromTree = (treeData, nodeId) => {
    const findNodePath = (nodes, targetId, currentPath = []) => {
        for (const node of nodes) {
            const newPath = [...currentPath, node.nodeName || `节点-${node.nodeId}`];
            if (node.nodeId === targetId) {
                return newPath;
            }
            if (node.children && node.children.length > 0) {
                const found = findNodePath(node.children, targetId, newPath);
                if (found) {
                    return found;
                }
            }
        }
        return null;
    };
    const path = findNodePath(treeData, nodeId);
    return path ? path.join(' > ') : `节点-${nodeId}`;
};

/**
 * 创建默认场景
 * @returns {Object} 默认场景对象
 */
export const createDefaultScene = () => {
    const defaultScene = {
        id: uuidv4(),
        name: '默认场景',
        firstFrameStrategy: '',
        lastFrameStrategy: '',
        isDefault: true
    };
    return defaultScene;
};

/**
 * 生成用例组选项
 * @param {Array} filteredList - 过滤后的列表
 * @returns {Array} 选项数组
 */
export const generatePlanTypeOptions = (filteredList) => {
    return filteredList.map((group) => ({
        value: group.groupId,
        key: group.groupId,
        label: `源于${group.groupName}`
    }));
};

/**
 * 获取分组的帧选项
 * @param {Array} availableOptions - 可用选项
 * @param {Array} stepOptions - 步骤选项
 * @param {string} label1 - 第一个标签
 * @param {string} label2 - 第二个标签
 * @returns {Array} 分组选项
 */
export const getGroupFrameOptions = (availableOptions, stepOptions, label1, label2) => {
    const _options = [];
    const _availableOptions = availableOptions.filter(
        (option, index, self) => index === self.findIndex((o) => o.value === option.value)
    );
    const _stepOptions = stepOptions.filter(
        (option, index, self) => index === self.findIndex((o) => o.value === option.value)
    );
    if (!isEmpty(_availableOptions)) {
        _options.push({
            label: label1 || '算法校准',
            title: label1 || '算法校准',
            options: _availableOptions
        });
    }
    if (!isEmpty(_stepOptions)) {
        _options.push({
            label: label2 || '机制校准',
            title: label2 || '机制校准',
            options: _stepOptions
        });
    }
    return _options;
};

/**
 * 处理树形数据，为每个用例节点添加操作系统类型的子节点
 * 根据当前选择的设备类型过滤显示对应的用例
 * @param {Array} tree - 原始树形数据
 * @param {string|number} currentDeviceType - 当前设备类型
 * @returns {Array} 处理后的树形数据
 */
export const processTreeData = (tree, currentDeviceType) => {
    const targetOsType = +currentDeviceType;

    return tree
        .map((item) => {
            if (item.nodeType === 2) {
                // 用例节点，需要添加操作系统类型的子节点
                const osChildren = [];

                // 根据设备类型添加对应的操作系统子节点
                if (targetOsType === 1 && item.androidCaseList && item.androidCaseList.length > 0) {
                    osChildren.push({
                        nodeId: `${item.nodeId}_android`,
                        nodeName: 'Android',
                        nodeType: 3, // 操作系统节点
                        parentId: item.nodeId,
                        osType: 1,
                        caseList: item.androidCaseList,
                        children: item.androidCaseList.map((caseItem) => ({
                            nodeId: `${item.nodeId}_android_${caseItem.caseId}`,
                            nodeName: caseItem.caseName || `用例-${caseItem.caseId}`,
                            nodeType: 4, // 用例叶子节点
                            parentId: `${item.nodeId}_android`,
                            caseId: caseItem.caseId,
                            osType: 1,
                            caseRootId: item.nodeId,
                            isLeaf: true
                        }))
                    });
                }

                if (targetOsType === 2 && item.iosCaseList && item.iosCaseList.length > 0) {
                    osChildren.push({
                        nodeId: `${item.nodeId}_ios`,
                        nodeName: 'iOS',
                        nodeType: 3, // 操作系统节点
                        parentId: item.nodeId,
                        osType: 2,
                        caseList: item.iosCaseList,
                        children: item.iosCaseList.map((caseItem) => ({
                            nodeId: `${item.nodeId}_ios_${caseItem.caseId}`,
                            nodeName: caseItem.caseName || `用例-${caseItem.caseId}`,
                            nodeType: 4, // 用例叶子节点
                            parentId: `${item.nodeId}_ios`,
                            caseId: caseItem.caseId,
                            osType: 2,
                            caseRootId: item.nodeId,
                            isLeaf: true
                        }))
                    });
                }

                // 如果没有对应操作系统的用例，则不显示该节点
                if (osChildren.length === 0) {
                    return null;
                }

                return {
                    ...item,
                    children: osChildren
                };
            } else if (item.children && item.children.length > 0) {
                // 目录节点，递归处理子节点
                const processedChildren = processTreeData(item.children, currentDeviceType).filter(
                    (child) => child !== null
                );
                if (processedChildren.length === 0) {
                    return null; // 如果没有子节点，则不显示该目录
                }
                return {
                    ...item,
                    children: processedChildren
                };
            }
            return item;
        })
        .filter((item) => item !== null);
};
