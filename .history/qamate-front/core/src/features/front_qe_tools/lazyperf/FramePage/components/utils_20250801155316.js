// 计算智能TTI
export const calculateAutoTTI = (record) => {
    const { correctDetail = {} } = record;
    const { auto = {} } = correctDetail;

    if (!auto.firstFrameTimestamp || !auto.lastFrameTimestamp) {
        return '--';
    }

    const tti = auto.lastFrameTimestamp - auto.firstFrameTimestamp;
    if (tti < 0) {
        return <span style={{ color: '#ff0000' }}>首帧大于尾帧, 异常</span>;
    }

    return `${tti} ms`;
};

export const calculateManualTTI = (record) => {
    const { correctDetail = {} } = record;
    const { auto = {}, manual = {} } = correctDetail;

    // 计算首尾帧时间戳 - 如果人工未校准则使用智能校准的结果
    const firstTimestamp = manual.firstFrameStatus === 1 ? manual.firstFrameTimestamp : auto.firstFrameTimestamp;
    const lastTimestamp = manual.lastFrameStatus === 1 ? manual.lastFrameTimestamp : auto.lastFrameTimestamp;

    if (!firstTimestamp || !lastTimestamp) {
        return '--';
    }

    const tti = lastTimestamp - firstTimestamp;
    if (tti < 0) {
        return <span style={{ color: '#ff0000' }}>首帧大于尾帧, 异常</span>;
    }

    return `${tti} ms`;
};
