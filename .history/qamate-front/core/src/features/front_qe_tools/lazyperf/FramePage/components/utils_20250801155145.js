// 计算智能TTI
export const calculateAutoTTI = (record) => {
    const { correctDetail = {} } = record;
    const { auto = {} } = correctDetail;

    if (!auto.firstFrameTimestamp || !auto.lastFrameTimestamp) {
        return '--';
    }

    const tti = auto.lastFrameTimestamp - auto.firstFrameTimestamp;
    if (tti < 0) {
        return <span style={{ color: '#ff0000' }}>首帧大于尾帧, 异常</span>;
    }

    return `${tti} ms`;
};
