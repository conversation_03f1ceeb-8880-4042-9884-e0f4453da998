import { useState, useEffect, useCallback } from 'react';
import { Space, Table, Tag, Button, Input, Popover, message, Spin } from 'antd';
import {
    RobotOutlined,
    UserOutlined,
    CloseCircleOutlined,
    BlockOutlined,
    CarryOutOutlined,
    DeleteRowOutlined,
    LoadingOutlined
} from '@ant-design/icons';
import { getSpeedRoundList, updateSpeedRound } from 'COMMON/api/front_qe_tools/lazyperf';
import { calculateAutoTTI } from './utils';
const { TextArea } = Input;

const CalibrateTable = ({
    planId,
    caseNodeId,
    sceneId,
    recordList,
    onCalibrate,
    checkMode = false,
    refreshRecordList,
    setRecordList
}) => {
    const [invalidComment, setInvalidComment] = useState('');
    // 初始化数据
    useEffect(() => {
        refreshRecordList();
    }, []);

    // 计算人工TTI


    // 获取记录状态
    const getRecordStatus = (record) => {
        const { correctDetail = {}, isValid } = record;
        const { auto = {}, manual = {} } = correctDetail;

        if (isValid === 1) {
            return {
                status: '已废弃',
                icon: <CloseCircleOutlined />,
                color: 'error'
            };
        }

        // 检查是否正在校准中（智能校准未完成）
        if (auto.firstFrameStatus === 0 || auto.lastFrameStatus === 0) {
            return {
                status: '校准中',
                icon: <LoadingOutlined />,
                color: 'warning'
            };
        }

        // 检查人工校准状态
        const manualStatus = Math.max(manual.firstFrameStatus || 0, manual.lastFrameStatus || 0);
        if (manualStatus === 1) {
            return {
                status: '人工校准',
                icon: <UserOutlined />,
                color: 'success'
            };
        }

        // 智能校准完成但未人工校准
        return {
            status: '智能校准',
            icon: <RobotOutlined />,
            color: 'processing'
        };
    };

    // 处理记录废弃
    const handleInvalidRecord = async (recordSceneId, comment = '') => {
        await updateSpeedRound({
            recordSceneId,
            isValid: 1,
            validComment: comment
        });

        // 更新本地状态
        const newRecordList = recordList.map((record) =>
            record.recordSceneId === recordSceneId
                ? { ...record, isValid: 1, invalidComment: comment }
                : record
        );
        setRecordList(newRecordList);
        message.success('记录已废弃');
    };

    // 处理记录恢复
    const handleRestoreRecord = async (recordSceneId) => {
        await updateSpeedRound({
            recordSceneId,
            isValid: 0,
            validComment: ''
        });

        // 更新本地状态
        const newRecordList = recordList.map((record) =>
            record.recordSceneId === recordSceneId
                ? { ...record, isValid: 0, invalidComment: '' }
                : record
        );
        setRecordList(newRecordList);
        message.success('记录已恢复');
    };

    //校准
    const handleCalibrate = (record) => {
        if (onCalibrate) {
            onCalibrate(record);
        }
    };

    const columns = [
        {
            title: 'ID',
            width: 100,
            dataIndex: 'recordSceneId',
            key: 'recordSceneId'
        },
        {
            title: '智能TTI',
            width: 200,
            key: 'smartTTI',
            render: (record) => {
                if (record.isValid === 1) {
                    return <span>--</span>;
                }
                return calculateAutoTTI(record);
            }
        },
        {
            title: '人工TTI',
            width: 200,
            key: 'manualTTI',
            render: (record) => {
                if (record.isValid === 1) {
                    return <span>--</span>;
                }
                return calculateManualTTI(record);
            }
        },
        {
            title: '状态',
            key: 'status',
            render: (record) => {
                const statusInfo = getRecordStatus(record);
                return (
                    <Tag icon={statusInfo.icon} color={statusInfo.color}>
                        {statusInfo.status}
                    </Tag>
                );
            }
        },
        {
            title: '操作',
            width: 300,
            key: 'operate',
            render: (record) => {
                if (record.isValid === 1) {
                    // 已废弃的记录显示恢复按钮
                    return (
                        <Space size="middle">
                            <Button
                                shape="round"
                                size="small"
                                icon={<CarryOutOutlined />}
                                onClick={() => handleRestoreRecord(record.recordSceneId)}
                            >
                                恢复
                            </Button>
                            {record.invalidComment && (
                                <span style={{ color: '#999' }}>
                                    因 {record.invalidComment} 被废弃
                                </span>
                            )}
                        </Space>
                    );
                }

                return (
                    <Space size="middle">
                        {/* <Button
                            shape="round"
                            size="small"
                            icon={<BlockOutlined />}
                            onClick={() => handleCalibrate(record)}
                        >
                            校准
                        </Button> */}
                        <Popover
                            title="废弃理由"
                            trigger="click"
                            content={
                                <div>
                                    <TextArea
                                        value={invalidComment}
                                        onChange={(e) => setInvalidComment(e.target.value)}
                                        placeholder="废弃备注, 可为空"
                                        autoSize={{ minRows: 3, maxRows: 5 }}
                                    />
                                    <Button
                                        danger
                                        style={{ marginTop: 5 }}
                                        onClick={() => {
                                            handleInvalidRecord(
                                                record.recordSceneId,
                                                invalidComment
                                            );
                                            setInvalidComment('');
                                        }}
                                    >
                                        确认
                                    </Button>
                                </div>
                            }
                        >
                            <Button danger shape="round" size="small" icon={<DeleteRowOutlined />}>
                                废弃
                            </Button>
                        </Popover>
                    </Space>
                );
            }
        }
    ];

    if (checkMode) {
        columns.splice(columns.length - 1, 0, {
            title: '检查',
            key: 'check',
            width: 100,
            render: () => (
                <Button shape="round" size="small" icon={<BlockOutlined />}>
                    检查
                </Button>
            )
        });
    }

    return (
        <Table
            style={{ width: '100%' }}
            size="small"
            columns={columns}
            dataSource={recordList}
            rowKey={(record) => record.recordSceneId}
            pagination={{
                showSizeChanger: false,
                hideOnSinglePage: true,
                showQuickJumper: true,
                pageSize: 10,
                showTotal: (total) => `共${total}条`
            }}
            bordered
        />
    );
};

export default CalibrateTable;
