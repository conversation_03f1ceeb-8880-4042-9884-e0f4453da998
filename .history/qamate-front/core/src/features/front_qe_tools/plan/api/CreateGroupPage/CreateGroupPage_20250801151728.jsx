import { useEffect, useState, useMemo, useRef, use } from 'react';
import { useNavigate } from 'umi';
import moment from 'moment';
import { stringifyUrl } from 'query-string';
import {
    Button,
    Descriptions,
    Form,
    Input,
    message,
    Radio,
    Popconfirm,
    Tabs,
    Tag,
    Tree,
    Spin,
    Tooltip,
    Badge,
    Select,
    Checkbox,
    Empty,
    Steps,
    TimePicker,
    InputNumber,
    Switch
} from 'antd';
import {
    AndroidOutlined,
    AppleOutlined,
    DownOutlined,
    FilterOutlined,
    FunnelPlotOutlined,
    CloudServerOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardContent } from 'COMMON/components/common/Card';
import { CardHeader } from 'COMMON/components/common/Card';
import { CardTitle } from 'COMMON/components/common/Card';
import NoContent from 'COMMON/components/common/NoContent';
import NodeIcon from 'COMMON/components/NewAddDropdown/components/NodeIcon';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import MemberSelect from 'COMMON/components/Select/MemberSelect';
import RenderTitle from 'FEATURES/front_qe_tools/plan/components/RenderTitle';
import { FILTER_DATA, filterObject } from 'COMMON/utils/commonUtils';
import {
    getDevicePoolList,
    getPlanTemplateDetail,
    getPlanTemplateList,
    createServerPlanTemplate,
    updateServerPlanTemplate,
    getServerPlanTemplateList,
    getServerPlanTemplateDetail,
    executeServerPlanTemplate
} from 'COMMON/api/front_qe_tools/plan/plan';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';
import { FILTER_INFO } from 'COMMON/components/Filter/const';
import Filter from 'COMMON/components/Filter';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import { planTypeOptions } from 'COMMON/utils/planUtils.js';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';
import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import DeviceType from 'FEATURES/front_qe_tools/plan/components/Setting/DeviceType';
import ServerEnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/ServerEnvParams';
import CaseNodeTree from 'FEATURES/front_qe_tools/plan/components/Setting/CaseNodeTree';
import CreatTemplateModal from 'FEATURES/front_qe_tools/plan/components/CreatTemplateModal';
import styles from './CreateGroupPage.module.less';

const { Search } = Input;

// 扁平树为数组
function flattenTree(node, parentId = null, flattenedTree = []) {
    const nodeCopy = deepcopy(node);
    nodeCopy.parentId = parentId;
    flattenedTree.push(nodeCopy);

    const children = node.children || [];
    for (const child of children) {
        flattenTree(child, node.nodeId, flattenedTree);
    }

    return flattenedTree;
}
// 从树结构中提取选中的节点，并生成包含任务的节点ID列表
function getSelectedNodeKeys(tree = [], treeNodeList, nodeList = []) {
    tree?.forEach((item) => {
        if (item.nodeType !== 1) {
            let node = treeNodeList?.find((v) => v.treeNodeId === item.nodeId);
            if (node) {
                node?.taskList?.forEach((task) => {
                    nodeList.push(item.nodeId + '-' + task.osType);
                });
            }
        } else {
            getSelectedNodeKeys(item.children, treeNodeList, nodeList);
        }
    });
    return nodeList;
}

// 获取节点是否被修改的映射
const getNodeIsChangeMap = (filterData) => {
    const nodeIsChangeMap = {};
    Object.keys(filterData).forEach((nodeId) => {
        nodeIsChangeMap[nodeId] = false;
    });
    return nodeIsChangeMap;
};

function CreateGroupPage(props) {
    const {
        currentSpace,
        currentModule,
        serverList,
        currentPlanGroup,
        planList,
        setPlanList,
        planTemplateList,
        setPlanTemplateList,
        currentPlan,
        setCurrentPlan
    } = props;
    const query = getQueryParams();
    const [currentStep, setCurrentStep] = useState(0);
    const [currentTemplateId, setCurrentTemplateId] = useState(null);
    const [loading, setLoading] = useState(true);
    const [groupId, setGroupId] = useState(null);
    const [serverShowMore, setServerShowMore] = useState(false);
    const [iosShowMore, setIosShowMore] = useState(false);
    const [androidShowMore, setAndroidShowMore] = useState(false);
    const [createLoading, setCreateLoading] = useState(false);
    const [selectedNode, setSelectedNode] = useState([]);
    const [caseNodeList, setCaseNodeList] = useState([]);
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [nodeTreeMapList, setNodeTreeMapList] = useState([]);
    const [planDetail, setPlanDetail] = useState({});
    const [groupList, setGroupList] = useState([]);
    const [filteredList, setFilteredList] = useState([]);
    const [cloudDeviceList, setCloudDeviceList] = useState({}); // 双端云设备列表
    const [envList, setEnvList] = useState([]); // 环境列表
    const [showOtherConfig, setShowOtherConfig] = useState(false); // 是否显示其他配置
    const [activeExecuteConfigKey, setActiveExecuteConfigKey] = useState('android');
    const [activeAlarmConfigKey, setActiveAlarmConfigKey] = useState('android');
    const [nodeCaseIsChangeMap, setNodeCaseIsChangeMap] = useState({});
    const [nodeCaseListMap, setNodeCaseListMap] = useState({});
    const [nodeCaseKesMap, setNodeCaseKesMap] = useState({});
    const [filterDataMap, setFilterDataMap] = useState({});
    const [treeNodaPathMap, setTreeNodaPathMap] = useState({});
    const [filterDataIsChangeMap, setFilterDataIsChangeMap] = useState({});
    const [selectNodeOrhalfCheckedKeys, setSelectNodeOrhalfCheckedKeys] = useState([]);
    const [firstStepData, setFirstStepData] = useState({});
    const [secondsStepData, setSecondsStepData] = useState({});
    const caseNodeTreeModalRef = useRef();
    const creatTemplateModalRef = useRef();
    const templateManagerRef = useRef();
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [commonConfigForm] = Form.useForm();
    const [androidConfigForm] = Form.useForm();
    const [serverConfigForm] = Form.useForm();
    const [iosConfigForm] = Form.useForm();
    const [androidAlarmForm] = Form.useForm();
    const [iosAlarmForm] = Form.useForm();
    const [serverAlarmForm] = Form.useForm();
    const [executeConfigForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const [creationMethod, setCreationMethod] = useState('custom');
    const [items, setItems] = useState(executeConfigItems);
    const [poolList, setPoolList] = useState([]); // 设备池列表
    const [templateList, setTemplateList] = useState([]); // 模板列表
    const navigate = useNavigate();
    // 标签选项（动态变更）
    const getTagOptions = (isLabelName) => {
        tagList.sort((a, b) => (a.username === username ? -1 : b.username === username ? 1 : 0));
        return tagList.map((item, index) => {
            const color = convertTagColorTypeToHexadecimal(item.color);
            return {
                key: 'tag' + index,
                value: item.id,
                name: item?.tagName,
                username: item.username,
                label: (
                    <>
                        {isLabelName ? (
                            <>
                                {item.tagName}({item?.username})
                            </>
                        ) : (
                            <>
                                <div
                                    className={styles.tagIcon}
                                    style={{
                                        backgroundColor: color,
                                        border: '#d9d9d9 solid 1px'
                                    }}
                                />
                                <div className={styles.tagName}>
                                    {item.tagName}
                                    <span className={styles.tagCreator}>({item?.username})</span>
                                </div>
                            </>
                        )}
                    </>
                )
            };
        });
    };

    const getOptions = (type, isLabelName = false) => {
        if (type === 'tag') {
            return getTagOptions(isLabelName);
        }
        if (type === 'link') {
            return [];
        }
        return FILTER_INFO[type]?.option;
    };

    const format = 'HH:mm';
    const getNodeList = (nodeTree, nodeList = []) => {
        nodeTree.forEach((item) => {
            if (!item?.children || item.children.length === 0) {
                nodeList.push(item.nodeId);
            }
            if (item?.children && item.children.length > 0) {
                getNodeList(item.children, nodeList);
            }
        });
        return nodeList;
    };
    const getFilterData = (nodeId, newFilterDataMap, isLabel = false) => {
        const filterTagList = [];
        const parentIdPath = [...(treeNodaPathMap[nodeId]?.parentPath || [])]; // 获取当前节点的父节点路径
        parentIdPath.push(nodeId);
        let filterData = filterObject(FILTER_DATA, ['stampResult', 'autoStatus', 'relation']);
        let isEnd = false;
        parentIdPath?.reverse().forEach((itemNodeId) => {
            // 忽略包含 '-' 的节点和自身节点
            if (itemNodeId !== 'root' && !isEnd && !`${itemNodeId}`.includes('-')) {
                // 合并选中结果
                const currentFilterData = (newFilterDataMap || filterDataMap)[itemNodeId];
                if (currentFilterData) {
                    Object.keys(filterData).forEach((type) => {
                        filterData[type] = {
                            ...filterData[type],
                            data: [
                                ...new Set([
                                    ...(filterData[type]?.data || []),
                                    ...(currentFilterData[type]?.data || []) // 添加默认空数组
                                ])
                            ]
                        };
                    });
                }
            }
            if (filterDataIsChangeMap[itemNodeId] === true && !`${itemNodeId}`.includes('-')) {
                isEnd = true;
            }
        });
        // 获取数组最后一项

        // const firstParentId = [...parentIdPath].pop();
        // if(parentIdPath.length >= 1){
        //     const firstParentId = parentIdPath[parentIdPath.length - 1]
        //     filterData = (newFilterDataMap || filterDataMap)[firstParentId.nodeId]
        // }
        if (isLabel) {
            Object.keys(filterData).forEach((type) => {
                filterTagList.push(
                    ...filterData[type].data.map((v) => {
                        return getOptions(type, true).find((op) => op.value === v).label;
                    })
                );
            });
        }

        return {
            filterTagList,
            filterData
        };
    };
    const getLeafNodeIds = (tree, keys = []) => {
        tree.map((ele) => {
            if (!ele.children || ele.children.length === 0) {
                keys.push(ele.nodeId);
            } else {
                getLeafNodeIds(ele.children, keys);
            }
        });
        return keys;
    };
    const selectGroupType = useMemo(() => {
        if (filteredList?.length > 0 && groupId) {
            return filteredList.find((item) => item.groupId === groupId)?.groupType;
        }
    }, [groupId, filteredList]);
    const executeConfigItems = [
        {
            key: 'server',
            value: '4',
            label: '服务端配置',
            icon: <CloudServerOutlined />
        }
    ];
    const getNewTree = (tree) => {
        return tree.map((item) => {
            if (item.nodeType === 2) {
                let _osTypeList;
                if (item.signType === 1 && item.osTypeList[0] === 3) {
                    _osTypeList = [3];
                } else if (item.signType === 1 && item.osTypeList.length > 1) {
                    _osTypeList = [item.osTypeList[0]];
                } else if (item.signType === 0 && item.osTypeList[0] === 3) {
                    _osTypeList = [1, 2];
                } else if (item.signType === 0 && item.osTypeList.length > 1) {
                    _osTypeList = item.osTypeList;
                } else {
                    _osTypeList = item.osTypeList;
                }
                item.children = _osTypeList.map((os) => ({
                    nodeId: item.nodeId + '-' + os,
                    os: os,
                    caseRootId: item.caseRootId,
                    caseNodeList: [],
                    signType: item.signType
                }));
                return item;
            } else {
                return { ...item, children: getNewTree(item.children) };
            }
        });
    };
    // 获取模板列表的函数
    const fetchTemplates = async () => {
        // TODO:
        const { templateList } = await getPlanTemplateList({
            moduleId: currentSpace?.id,
            templateType: currentPlanGroup?.planType
        });
        setTemplateList(templateList ?? []);
    };

    // useEffect(() => {
    //     if (creationMethod === 'template') {
    //         fetchTemplates();
    //     }
    // }, [creationMethod]);
    useEffect(() => {
        if (
            !selectNodeOrhalfCheckedKeys.length &&
            selectedNode.length > 0 &&
            !isEmpty(treeNodaPathMap)
        ) {
            const newSelectNodeOrhalfCheckedKeys = [];
            // 获取所有父级节点
            selectedNode.forEach((item) => {
                const parentPath = treeNodaPathMap[item]?.parentPath;
                if (parentPath) {
                    newSelectNodeOrhalfCheckedKeys.push(item, ...parentPath);
                }
            });

            setSelectNodeOrhalfCheckedKeys([...new Set(newSelectNodeOrhalfCheckedKeys)]);
        }
    }, [selectedNode, selectNodeOrhalfCheckedKeys, treeNodaPathMap]);
    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            let filter = groupList ?? [];
            setFilteredList(filter);
            setGroupId(filter[0]?.groupId);
            getTreeNodeListWithPlanType(filter[0]?.groupId, groupList);
            // console.log(filteredList, 'filteredList[0]?.groupId');
            // getTreeNodeListWithPlanType(filteredList[0]?.groupId, groupList);
            // 获取设备池列表
            const { poolList } = await getDevicePoolList({
                moduleId: currentSpace?.id,
                poolType: 3 // 设备池类型 1-Android 2-iOS 3-Server
            });
            setPoolList(poolList);
        }
        func();
    }, [currentSpace?.id, query?.planType]);
    // 生成组建路径映射表
    const createTreePath = (treeData) => {
        const pathTree = {};
        // 遍历所有根节点
        treeData.forEach((rootNode) => {
            buildPathTree(rootNode, [], pathTree);
        });
        setTreeNodaPathMap(pathTree);
    };
    const getTreeNodeListWithPlanType = async (groupId, groupList) => {
        try {
            setLoading(true);
            let group;
            //  集成回归用例组
            // if (planType === 1) {
            //     group = groupList.find(item => item?.groupType === 2);
            // }
            // //  本地执行用例组
            // if (planType === 2) {
            //     group = groupList.find(item => item?.groupType === 4);
            // }
            // group = groupList.find(item => item?.groupType === planType);
            if (!groupId) {
                return;
            }
            let { tree } = await getTreeNodeList({ groupId: groupId });
            setDirectoryTreeData(getNewTree(tree));
            setSearchValue('');
            let nodeKeys = tree.map((item) => flattenTree(item)).flat() ?? [];
            setNodeTreeMapList(nodeKeys);
            // 转化成map
            const newNodeCaseListMap = {};
            nodeKeys.forEach((v) => {
                newNodeCaseListMap[v.nodeId] = v;
            });
            setNodeCaseListMap(newNodeCaseListMap);
            createTreePath(getNewTree(tree));

            if (!query?.planId) {
                const initFilterDataMap = {};
                nodeKeys.forEach((item) => {
                    if (!selectedNode.includes(item.nodeId)) {
                        initFilterDataMap[item.nodeId] = filterObject(FILTER_DATA, [
                            'stampResult',
                            'autoStatus'
                        ]);
                    }
                });
                setFilterDataMap(initFilterDataMap);
            }
            // 获取 创建第一次进入默认全部key
            setExpandedKeys(nodeKeys.map((item) => item.nodeId));
            setLoading(false);
        } catch (err) {
            console.log(err);
        }
    };

    // useEffect(() => {
    //     // console.log(groupList, groupId, 111111);
    //     if (groupList && groupId) {
    //         getTreeNodeListWithPlanType(groupId, groupList);
    //     }
    // }, [groupId, filteredList]);
    // useEffect(() => {
    //     setGroupId(filteredList[0]?.groupId);
    // }, [filteredList]);
    // 获取已创建用例的对应内容
    const getExecutePlanCaseNodeList = (tree, nodeList = []) => {
        tree.map((item) => {
            // 用例
            if (item.nodeType === 2 && item.caseNodeIdList) {
                for (let _item of item.caseNodeList) {
                    nodeList.push({
                        nodeId: item.nodeId,
                        caseRootId: item.caseRootId,
                        osType: _item.osType,
                        caseIdList: _item?.caseNodeIdList ?? []
                    });
                }
            }
            if (!isEmpty(item.children) && item.nodeType === 1) {
                getExecutePlanCaseNodeList(item.children);
            }
        });
        return nodeList;
    };
    function buildPathTree(node, parentPath, pathTree) {
        const currentNodeId = node.nodeId;
        const children = node.children || [];

        // 先处理子节点，后序遍历确保子节点信息已生成
        for (const child of children) {
            const childParentPath = [...parentPath, currentNodeId];
            buildPathTree(child, childParentPath, pathTree);
        }

        // 收集所有子节点ID（包括后代）
        let childrenNodeIdTree = [];
        for (const child of children) {
            childrenNodeIdTree.push(child.nodeId);
            childrenNodeIdTree.push(...(pathTree[child.nodeId]?.childrenNodeIdTree || []));
        }

        // 记录当前节点的路径信息
        pathTree[currentNodeId] = {
            parentPath: [...parentPath],
            childrenNodeIdTree: [...new Set(childrenNodeIdTree)] // 去重（根据需求可选）
        };
    }
    useEffect(() => {
        if (query?.planId) {
            let currentPlanDetail = planList?.find((item) => item?.id === +query?.planId);
            let nodeList = getSelectedNodeKeys(
                currentPlanDetail?.nodeTree ?? [],
                currentPlanDetail?.treeNodeList
            );
            setSelectedNode(nodeList);
            let caseList = getExecutePlanCaseNodeList(currentPlanDetail?.nodeTree ?? []);
            setCaseNodeList(caseList);
            setExpandedKeys([
                ...(currentPlanDetail?.nodeTree
                    ? expandedKeys.filter((item) => nodeList.includes(item?.nodeId))
                    : expandedKeys
                )?.map((item) => item?.nodeId)
            ]);
        }
    }, [planList]);

    useEffect(() => {
        if (query?.planTemplateId) {
            // let currentPlanDetail = planList?.find((item) => item?.id === +query?.planId);
            // if (!currentPlanDetail?.id) {
            //     return;
            // }
            // getPlanDetail({ planIdList: [+currentPlanDetail?.id] }).then((res) => {

            // 改为获取模板详情
            getServerPlanTemplateDetail({
                planTemplateId: query?.planTemplateId
                // moduleId: currentSpace?.id
            })
                .then((res) => {
                    const templateData = res;
                    const filterData = {};
                    const nodeIsChangeMap = {};
                    const nodeCaseKesMap = {};

                    // 处理节点树和选中节点
                    let nodeList = [];
                    if (templateData.nodeTree) {
                        nodeList = templateData.nodeTree
                            .map((node) => {
                                if (node.caseNodeList && node.caseNodeList.length > 0) {
                                    return node.caseNodeList.map(
                                        (caseNode) => `${node.nodeId}-${caseNode.osType}`
                                    );
                                }
                                return `${node.nodeId}-${node.osType}`;
                            })
                            .flat();
                    }

                    setSelectedNode(nodeList);
                    // 处理筛选条件
                    setFilterDataMap(filterData);
                    setFilterDataIsChangeMap(getNodeIsChangeMap(filterData));
                    setNodeCaseIsChangeMap(nodeIsChangeMap);
                    setNodeCaseKesMap(nodeCaseKesMap);

                    // 初始化的时候，把原有的数据填入表单
                    commonConfigForm.setFieldValue('name', templateData.planTemplateName ?? '');
                    commonConfigForm.setFieldValue('nodeTree', nodeList ?? '');

                    // 处理执行时间配置
                    if (templateData.executeTime) {
                        if (templateData.executeTime.cron) {
                            commonConfigForm.setFieldValue('executeType', 1);
                            commonConfigForm.setFieldValue('cron', templateData.executeTime.cron);
                        } else if (templateData.executeTime.frequency) {
                            commonConfigForm.setFieldValue('executeType', 2);
                            commonConfigForm.setFieldValue(
                                'frequency',
                                templateData.executeTime.frequency
                            );
                        }

                        // 如果有执行时间相关配置 展示
                        if (
                            templateData.executeTime.timePeriod ||
                            templateData.executeTime.cron ||
                            templateData.executeTime.frequency
                        ) {
                            commonConfigForm.setFieldValue('enableExecuteTime', true);
                        }
                        if (templateData.executeTime.timePeriod) {
                            commonConfigForm.setFieldValue('timePeriod', [
                                moment(templateData.executeTime.timePeriod.startTime, 'HH:mm'),
                                moment(templateData.executeTime.timePeriod.endTime, 'HH:mm')
                            ]);
                        }
                    } else {
                        commonConfigForm.setFieldValue('executeType', 1);
                    }

                    // 处理用例列表
                    if (templateData.nodeTree) {
                        setCaseNodeList(
                            templateData.nodeTree
                                .map((node) => {
                                    if (node.caseNodeList && node.caseNodeList.length > 0) {
                                        return node.caseNodeList.map((caseNode) => ({
                                            nodeId: node.nodeId,
                                            osType: caseNode.osType,
                                            caseIdList: caseNode.caseNodeIdList || [],
                                            caseRootId: node.caseRootId,
                                            filter: node.filterList
                                        }));
                                    }
                                    return {
                                        nodeId: node.nodeId,
                                        osType: node.osType,
                                        caseIdList: [],
                                        caseRootId: node.caseRootId,
                                        filter: node.filterList
                                    };
                                })
                                .flat()
                        );
                    }

                    // 处理执行配置
                    if (templateData.planTemplateParams?.cloudParams) {
                        const serverParam = templateData.planTemplateParams.cloudParams.find(
                            (p) => p.type === 4
                        );
                        if (serverParam) {
                            executeConfigForm.setFieldsValue({
                                poolId: serverParam.poolList?.[0],
                                cloudRetry: serverParam.retryTimes > 0,
                                retryTimes: serverParam.retryTimes || 0
                            });

                            // 设置环境参数
                            setCreatePlanOption((prev) => ({
                                ...prev,
                                executeConfig: {
                                    ...prev.executeConfig,
                                    server: {
                                        ...prev.executeConfig.server,
                                        envParams: serverParam.envParams || {}
                                    }
                                }
                            }));

                            // 设置报警配置
                            serverAlarmForm.setFieldsValue({
                                toid: serverParam.alarmInfo?.toid,
                                webhook: serverParam.alarmInfo?.webhook,
                                statusList: serverParam.alarmInfo?.statusList || [3, 4],
                                atuseridName: serverParam.alarmInfo?.atuseridName
                            });
                        }
                    }

                    // 设置用例组ID和组类型
                    if (templateData.planTemplateParams?.groupId) {
                        setGroupId(templateData.planTemplateParams.groupId);
                        getTreeNodeListWithPlanType(
                            templateData.planTemplateParams.groupId,
                            groupList
                        );
                    }
                    // 展开选中的节点
                    setExpandedKeys([
                        ...(nodeList.length > 0
                            ? expandedKeys.filter((item) =>
                                  nodeList.map((v) => +v.split('-')[0]).includes(item)
                              )
                            : expandedKeys)
                    ]);
                })
                .catch((error) => {
                    console.error('获取模板详情失败:', error);
                    messageApi.error('获取模板详情失败');
                });
        }
    }, [commonConfigForm, currentSpace?.id, query?.planTemplateId, groupList]);

    const onSubmit = async () => {
        try {
            // await androidAlarmForm?.validateFields();
            // await iosAlarmForm?.validateFields();
            await serverAlarmForm?.validateFields();
        } catch (e) {
            messageApi.warning('请填写完整表单');
            return;
        }
        try {
            // const commonConfigFormValues = commonConfigForm.getFieldsValue();
            let newNodeTree =
                firstStepData?.nodeTree?.filter((item) => ('' + item).includes('-')) || [];
            let treeNodeIdList = getTreeNodeIdList(
                directoryTreeData,
                [...new Set(newNodeTree?.map((item) => +(item + '').split('-')[0]))],
                newNodeTree
            );
            let planTemplateId;
            for (let item of treeNodeIdList) {
                let caseDetail = caseNodeList?.find(
                    (it) => it?.nodeId === item?.treeNodeId && it.osType === item?.osType
                );
                if (caseDetail) {
                    item.caseNodeList = caseDetail.caseIdList;
                } else {
                    item.caseNodeList = [];
                }
                // let list = caseNodeList?.find(
                //     (it) => it?.nodeId === item?.treeNodeId && it.osType === item?.osType
                // )?.caseIdList;
                // if (list) {
                //     item.caseNodeList = list;
                // }
                if (!nodeCaseIsChangeMap[`${item.treeNodeId}-${item.osType}`]) {
                    if (caseDetail?.filter) {
                        item.filter = caseDetail.filter;
                    } else {
                        // console.log(item, '兜底逻辑', nodeCaseListMap[item.treeNodeId]);
                        // 兜底逻辑，如果没有改变过筛选条件，则沿用之前的筛选条件
                        const { filterData } = getFilterData(
                            `${item.treeNodeId}-${item.osType}`,
                            filterDataMap
                        );
                        const filter = formatFilterData(filterData);
                        // 获取当前结点的caseNodeId
                        const caseRootId = nodeCaseListMap[item.treeNodeId]?.caseRootId;
                        item.filter = {
                            caseRootId: caseRootId,
                            ...filter
                        };
                    }
                }
            }
            // 没有改过

            setCreateLoading(true);
            const executeConfigFormValues = secondsStepData?.executeConfig;
            const serverAlarmFormValues = serverAlarmForm.getFieldsValue();
            let params = {
                moduleId: currentSpace?.id,
                planTemplateName: firstStepData.name,
                planTemplateParams: {
                    moduleId: currentSpace?.id,
                    name: firstStepData.name,
                    planType: 6, // 接口测试计划
                    groupId: groupId,
                    treeNodeIdList: treeNodeIdList,
                    cloudParams: [
                        {
                            type: 4,
                            poolList: [executeConfigFormValues?.poolId],
                            retryTimes: executeConfigFormValues?.retryTimes,
                            envParams: {
                                envId: createPlanOption?.executeConfig?.server?.envParams?.envId,
                                envDetail: {
                                    envId: createPlanOption?.executeConfig?.server?.envParams
                                        ?.envId,
                                    paramList:
                                        createPlanOption?.executeConfig?.server?.envParams?.paramList?.map(
                                            (item) => ({
                                                paramKey: item.paramKey,
                                                envValue: item.envValue
                                            })
                                        ),
                                    serverList:
                                        createPlanOption?.executeConfig?.server?.envParams?.serverList?.map(
                                            (item) => ({
                                                serverId: item.serverId,
                                                envValue: item.envValue
                                            })
                                        ),
                                    dbList: createPlanOption?.executeConfig?.server?.envParams?.dbList?.map(
                                        (item) => ({
                                            dbId: item.dbId,
                                            envValue: item.envValue
                                        })
                                    ),
                                    redisList:
                                        createPlanOption?.executeConfig?.server?.envParams?.redisList?.map(
                                            (item) => ({
                                                redisId: item.redisId,
                                                envValue: item.envValue
                                            })
                                        )
                                }
                            },
                            // 报警
                            alarmInfo: {
                                toid: serverAlarmFormValues?.toid
                                    ? +serverAlarmFormValues?.toid
                                    : null, // 群号
                                webhook: serverAlarmFormValues?.webhook, // 机器人地址
                                atuseridName: serverAlarmFormValues?.atuseridName, // 需@的人
                                statusList: serverAlarmFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                            }
                        }
                    ]
                }
            };
            // 只有当启用了执行时间时，才添加相关配置
            if (firstStepData.enableExecuteTime) {
                if (firstStepData.cron) {
                    params.executeTime = {
                        cron: firstStepData.cron
                    };
                }
                // 时间段
                if (!isEmpty(firstStepData?.timePeriod)) {
                    params.executeTime = {
                        ...params.executeTime,
                        timePeriod: {
                            startTime: firstStepData?.timePeriod?.[0].format('HH:mm'),
                            endTime: firstStepData?.timePeriod?.[1].format('HH:mm')
                        }
                    };
                }
                if (firstStepData?.frequency) {
                    params.executeTime = {
                        ...params.executeTime,
                        frequency: firstStepData.frequency
                    };
                }
            }
            // return;
            let planId = null;
            // 更新
            if (query?.planTemplateId) {
                await updateServerPlanTemplate({
                    ...params,
                    planTemplateId: query?.planTemplateId
                });
                planTemplateId = query?.planTemplateId;
            } else {
                // 创建
                const res = await createServerPlanTemplate(params);
                planTemplateId = res?.planTemplateId;
                // 新建时自动执行一次
                const executeResult = await executeServerPlanTemplate({
                    planTemplateId: planTemplateId
                });
                planId = executeResult?.planId;
            }
            const res = await getServerPlanTemplateList({
                moduleId: currentSpace?.id,
                pageSize: parseInt((window.innerHeight - 110) / 30 - 1, 10),
                pageIndex: 1
                // planType: currentPlanGroup?.planType
            });
            setPlanTemplateList(res?.planTemplateList);

            setCreateLoading(false);
            navigate(
                stringifyUrl({
                    url: '/' + currentModule + '/api/index',
                    query: {
                        moduleId: currentSpace?.id,
                        // planType: query?.planType ?? 1,
                        planTemplateId: planTemplateId,
                        ...(planId ? { planId } : {}),
                        'filters[planId]': query?.['filters[planId]']
                    }
                })
            );
        } catch (err) {
            console.log(err?.message ?? err, '创建计划失败');
            setCreateLoading(false);
        }
    };

    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };

    // 递归过滤隐藏菜单
    const getTreeNodeIdList = (tree = [], nodeList, nodeListWithOs, arr = []) => {
        if (!tree.length) {
            return [];
        }
        for (let item of tree) {
            // 若无子节点且不在list中，则不显示
            if (item.nodeType === 1) {
                getTreeNodeIdList(item?.children, nodeList, nodeListWithOs, arr);
            } else if (!nodeList.includes(item.nodeId)) {
                continue;
            } else if (nodeList.includes(item.nodeId)) {
                [1, 2, 3, 4, 5, 6].forEach((os) => {
                    if (nodeListWithOs.includes(item.nodeId + '-' + os)) {
                        arr.push({
                            treeNodeId: item?.nodeId,
                            osType: os
                        });
                    }
                });
            }
        }
        return arr;
    };

    // 获取云端设备池列表
    const obtainCloudDeviceList = async () => {
        if (!currentSpace?.id) {
            return;
        }
        let androidRes = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 1
        });
        let iosRes = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 2
        });
        setCloudDeviceList({
            android: androidRes?.poolList,
            ios: iosRes?.poolList
        });
    };

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        if (!currentSpace?.id) {
            return;
        }
        // let androidRes = await getEnvList({
        //     moduleId: currentSpace?.id,
        //     osType: 1
        // });
        // let iosRes = await getEnvList({
        //     moduleId: currentSpace?.id,
        //     osType: 2
        // });
        let serverRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 4
        });
        setEnvList({
            // android: androidRes.envList,
            // ios: iosRes.envList,
            server: serverRes?.envList
        });
    };
    const getSelectedCaseNodeList = (planDetail) => {
        const caseNodeList = [];
        const getNodeList = (nodeList) => {
            nodeList?.forEach((item) => {
                // nodeType 为2的节点
                if (item?.nodeType === 2) {
                    // 遍历 caseNodeList 选中的节点
                    item?.caseNodeList?.forEach((node) => {
                        caseNodeList.push({
                            nodeId: item.nodeId,
                            osType: node?.osType,
                            caseIdList: node.caseNodeIdList,
                            caseRootId: item.caseRootId
                        });
                    });
                } else {
                    // 目录
                    getNodeList(item.children);
                }
            });
        };
        getNodeList(planDetail?.nodeTree);
        return caseNodeList;
    };
    useEffect(() => {
        obtainCloudDeviceList();
        obtaionEnvParamsList();
    }, []);
    const formatFilterData = (filterData) => {
        const getValue = (field) => {
            return filterData[field].data.map((v) => `${v}`);
        };
        const filter = {
            isAccess: {
                values: getValue('access') // 是否准入 0-非准入 1-准入
            },
            priority: {
                // 节点优先级
                values: getValue('priority')
            },
            tagInfo: {
                values: getValue('tag')
            },
            executionType: {
                // 节点类型, 1-M(人工) 2-A(自动) 4:R（半自动
                values: getValue('executeType')
            },
            record: {
                // 录制信息
                values: getValue('record') // 0-未录制 1-已录制
            }
        };
        return filter;
    };

    const updateSelectKeys = (filterNode, newFilterDataMap) => {
        // 更新当前选择的节点
        const caseNodeList = getLeafNodeIds([filterNode]);
        caseNodeList
            .filter((v) => typeof v === 'string' && v.includes('-'))
            .forEach((v) => {
                const [nodeId, osType] = v.split('-');
                const { filterData } = getFilterData(nodeId, newFilterDataMap);
                const filter = formatFilterData(filterData);
                // 获取当前结点的caseNodeId
                const caseRootId = nodeCaseListMap[nodeId]?.caseRootId;
                setCaseNodeList((list) => {
                    const index = list.findIndex(
                        (v) => v.nodeId === +nodeId && v.osType === +osType
                    );
                    if (index !== -1) {
                        list[index].filter = { caseRootId: caseRootId, ...filter };
                    } else {
                        list.push({
                            nodeId: +nodeId,
                            caseRootId,
                            osType: +osType,
                            caseIdList: [],
                            filter: {
                                caseRootId: caseRootId,
                                ...filter
                            }
                        });
                    }
                    return [...list];
                });
            });
    };
    const getFilterComponent = (node) => {
        const nodeId = node.nodeId;
        return (
            <span style={{ fontSize: 12, color: '#448ef7' }}>
                {/* {nodeId} */}
                <Filter
                    type="check"
                    title="默认勾选"
                    caseNode={[]}
                    filterData={{ ...getFilterData(nodeId, filterDataMap)?.filterData }}
                    handleFilterData={(value, type, activeKey) => {
                        const originFilterData = getFilterData(nodeId, filterDataMap).filterData;
                        let newFilterData = {
                            ...originFilterData,
                            [type]: {
                                activeKey: 'belong',
                                data: value
                            }
                        };
                        const newFilterDataMap = {
                            ...filterDataMap,
                            [nodeId]: newFilterData
                        };
                        setFilterDataMap(newFilterDataMap);
                        setFilterDataIsChangeMap({
                            ...filterDataIsChangeMap,
                            [nodeId]: true
                        });
                        updateSelectKeys(node, newFilterDataMap);
                    }}
                    hiddenNoBelong
                    clearFilterData={(data) => {
                        const newFilterDataMap = {
                            ...filterDataMap,
                            [nodeId]: data
                        };
                        setFilterDataMap(newFilterDataMap);
                        updateSelectKeys(node, newFilterDataMap);
                    }}
                >
                    <Tooltip title="快速勾选用例" placement="right">
                        <span
                            style={{
                                paddingLeft: 10
                            }}
                        >
                            <Badge
                                dot={Object.values(getFilterData(nodeId)?.filterData || {}).some(
                                    (value) => !isEmpty(value?.data)
                                )} // 一个条件为非空.true
                                size="small"
                            >
                                <FilterOutlined className={styles.filterIcon} />
                            </Badge>
                        </span>
                    </Tooltip>
                </Filter>
            </span>
        );
    };
    // 递归过滤隐藏菜单
    const treeData = useMemo(() => {
        const loop = (data) => {
            let newData = [];
            data.forEach((item) => {
                const strTitle = !item?.nodeName || item?.nodeName === null ? '' : item.nodeName;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const title =
                    index > -1 ? (
                        <span>
                            {beforeStr}
                            <span className={styles.highlight}>{searchValue}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span>{strTitle}</span>
                    );

                // 检查是否有 osType 为 4 的子节点
                const hasOsType4Child = (node) => {
                    if (node.nodeType === 2) {
                        return node.children?.some((child) => child.os === 4);
                    }
                    return node.children?.some(hasOsType4Child);
                };

                if (item?.nodeType === 1) {
                    const children = loop(item.children);
                    // 只有当有 osType 为 4 的子节点时才显示父节点
                    if (children.length > 0 || hasOsType4Child(item)) {
                        newData.push({
                            title: (
                                <>
                                    <NodeIcon type={item.nodeType} />
                                    {title}
                                    {selectNodeOrhalfCheckedKeys.includes(item.nodeId) &&
                                        getFilterComponent(item)}
                                </>
                            ),
                            key: item.nodeId,
                            value: item.nodeId,
                            children
                        });
                    }
                } else if (item?.nodeType === 2) {
                    const children = loop(item.children);
                    // 只有当有 osType 为 4 的子节点时才显示父节点
                    if (children.length > 0 || hasOsType4Child(item)) {
                        newData.push({
                            title: (
                                <>
                                    <NodeIcon type={item.nodeType} />
                                    {title}
                                    {selectNodeOrhalfCheckedKeys.includes(item.nodeId) && (
                                        <>{getFilterComponent(item)}</>
                                    )}
                                </>
                            ),
                            key: item.nodeId,
                            value: item.nodeId,
                            children
                        });
                    }
                } else if (item.os === 4) {
                    let curNodeList =
                        caseNodeList?.find((list) => {
                            let nodeId = item?.nodeId?.split('-')[0];
                            return +list?.nodeId === +nodeId && +item?.os === +list?.osType;
                        })?.caseIdList ?? [];
                    newData.push({
                        title: (
                            <>
                                <RenderTitle os={item.os} />
                                {selectedNode.includes(item.nodeId) && (
                                    <Tooltip title="筛选需要创建的用例, 默认全选" placement="right">
                                        <Badge dot={curNodeList?.length > 0}>
                                            <FunnelPlotOutlined
                                                className={styles.filter}
                                                onClick={() => {
                                                    let filterData = {};
                                                    if (!nodeCaseIsChangeMap[item.nodeId]) {
                                                        filterData =
                                                            getFilterData(item.nodeId)
                                                                ?.filterData || {};
                                                    }
                                                    caseNodeTreeModalRef?.current?.show(
                                                        item.caseRootId,
                                                        item.os,
                                                        +item.nodeId.split('-')?.[0],
                                                        caseNodeList,
                                                        filterData
                                                    );
                                                }}
                                            />
                                        </Badge>
                                    </Tooltip>
                                )}
                                {!nodeCaseIsChangeMap[item.nodeId] &&
                                    selectNodeOrhalfCheckedKeys.includes(item.nodeId) &&
                                    getFilterData(item.nodeId, null, true)?.filterTagList?.map(
                                        (name) => {
                                            return (
                                                <Tag className={styles.typeTag} key={name}>
                                                    {name}
                                                </Tag>
                                            );
                                        }
                                    )}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId
                    });
                }
            });
            return newData;
        };
        return loop(directoryTreeData);
    }, [
        searchValue,
        directoryTreeData,
        selectedNode,
        caseNodeList,
        filterDataMap,
        nodeCaseListMap,
        nodeCaseIsChangeMap,
        filterDataIsChangeMap,
        selectNodeOrhalfCheckedKeys
    ]);

    const onSearchValueChange = (e) => {
        const { value } = e.target;
        const newExpandedKeys = nodeTreeMapList
            .map((item) => {
                if (`${item?.nodeName || ''}`.includes(value)) {
                    return item?.nodeId || null;
                }
                return null;
            })
            .filter((item) => item !== null);
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(true);
        setSearchValue(value);
    };
    // console.log(treeData);

    // const planTypeOptions = [
    //     {
    //         value: 1,
    //         label: '源于集成回归用例组'
    //     },
    //     {
    //         value: 2,
    //         disabled: +query?.planType === 5,
    //         label: '源于本地执行用例组'
    //     }
    // ];

    const AlarmConfigForm = ({ activeKey, form }) => {
        const [showMore, setShowMore] = useState(false);
        return (
            <Form
                form={form}
                layout="vertical"
                requiredMark={false}
                colon={false}
                initialValues={{
                    statusList: [3, 4]
                }}
            >
                <CardTitle text="如流群配置" style={{ marginTop: 0 }} />
                <Form.Item label="如流群" name="toid">
                    <Input
                        placeholder="请输入如流群"
                        allowClear
                        onChange={(e) => {
                            if (isEmpty(e.target.value)) {
                                setShowMore(false);
                            } else {
                                setShowMore(true);
                            }
                        }}
                    />
                </Form.Item>
                <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) => {
                        const toidValue = getFieldValue('toid');
                        return (
                            <>
                                {toidValue || toidValue === 0 ? (
                                    <div>
                                        <Form.Item label="报警机器人地址" name="webhook">
                                            <Input placeholder="请输入报警机器人地址" allowClear />
                                        </Form.Item>
                                        <Form.Item label="@ 的人" name="atuseridName">
                                            <MemberSelect
                                                mode="multiple"
                                                variant="outlined"
                                                placeholder="请选择 @ 的人邮箱前缀"
                                            />
                                        </Form.Item>
                                        <CardTitle text="用例报警配置" style={{ marginTop: 0 }} />
                                        <Form.Item label="报警类型" name="statusList">
                                            <Checkbox.Group>
                                                <Checkbox value={2}>成功</Checkbox>
                                                <Checkbox value={3}>失败</Checkbox>
                                                <Checkbox value={4}>异常</Checkbox>
                                            </Checkbox.Group>
                                        </Form.Item>
                                    </div>
                                ) : null}
                            </>
                        );
                    }}
                </Form.Item>
            </Form>
        );
    };

    const steps = [
        {
            title: '任务信息',
            content: (
                <div>
                    <Form
                        form={commonConfigForm}
                        layout="vertical"
                        requiredMark={false}
                        colon={false}
                        initialValues={{
                            executeType: 1 // 默认选择Cron表达式
                        }}
                    >
                        <CardTitle text="任务名称" />
                        <Form.Item
                            name="name"
                            rules={[
                                {
                                    required: true,
                                    message: '请输入任务名称'
                                }
                            ]}
                        >
                            <Input placeholder="请输入内容" allowClear />
                        </Form.Item>
                        <CardTitle text="执行配置" />
                        <Form.Item
                            name="enableExecuteTime"
                            valuePropName="checked"
                            initialValue={false}
                        >
                            <Checkbox>执行时间配置</Checkbox>
                        </Form.Item>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) =>
                                prevValues.enableExecuteTime !== currentValues.enableExecuteTime
                            }
                        >
                            {({ getFieldValue }) => {
                                return getFieldValue('enableExecuteTime') ? (
                                    <>
                                        <Form.Item
                                            // label="执行时间"
                                            name="executeType"
                                            labelCol={{ span: 8 }}
                                            wrapperCol={{ span: 16 }}
                                        >
                                            <Radio.Group>
                                                <Radio value={1}>Cron 表达式</Radio>
                                                <Radio value={2}>自定义</Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) =>
                                                prevValues.executeType !== currentValues.executeType
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                return (
                                                    <>
                                                        {getFieldValue('executeType') === 1 && (
                                                            <>
                                                                <div>
                                                                    <Form.Item
                                                                        name="cron"
                                                                        label="Cron 表达式"
                                                                    >
                                                                        <Input
                                                                            placeholder="请输入 Cron 表达式 （非必填）"
                                                                            allowClear
                                                                        />
                                                                    </Form.Item>
                                                                </div>
                                                            </>
                                                        )}
                                                        {getFieldValue('executeType') === 2 && (
                                                            <>
                                                                <div>
                                                                    <Form.Item
                                                                        name="frequency"
                                                                        label="执行频率"
                                                                        tooltip="计划的执行频率，最小值：120"
                                                                    >
                                                                        <InputNumber
                                                                            style={{
                                                                                width: '100%'
                                                                            }}
                                                                            placeholder="请输入执行频率 (单位: 分钟) （非必填）"
                                                                            allowClear
                                                                            min={120}
                                                                            addonAfter={'分钟'}
                                                                        />
                                                                    </Form.Item>
                                                                </div>
                                                            </>
                                                        )}
                                                        <Form.Item
                                                            name="timePeriod"
                                                            label="执行时间段"
                                                        >
                                                            <TimePicker.RangePicker
                                                                format={format}
                                                                style={{ width: '100%' }}
                                                                placeholder={[
                                                                    '开始时间',
                                                                    '结束时间'
                                                                ]}
                                                            />
                                                        </Form.Item>
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                    </>
                                ) : null;
                            }}
                        </Form.Item>
                        <div>{/* <CardTitle text="Cron 表达式" /> */}</div>
                        {/* <Form.Item
                            name="cloudRetryTimes"
                            label="重试次数"
                            rules={[
                                {
                                    required: true,
                                    message: ''
                                }
                            ]}
                            initialValue={1}
                            tooltip="单计划执行失败后自动重试的最大次数，范围：0-3"
                        >
                            <InputNumber
                                placeholder="请输入重试次数"
                                min={0}
                                max={3}
                                style={{ width: '100%' }}
                            />
                        </Form.Item> */}
                        <CardTitle text="测试用例">
                            <Select
                                variant="borderless"
                                style={{
                                    width: +query?.planType !== 5 ? 161 : 145
                                }}
                                className={styles.selecPlanType}
                                popupMatchSelectWidth={false}
                                size="small"
                                value={groupId}
                                suffixIcon={+query?.planType !== 5 ? <DownOutlined /> : null}
                                options={planTypeOptions()}
                                onChange={(value) => {
                                    // setPlanType(value);
                                    setGroupId(value);
                                    getTreeNodeListWithPlanType(value, groupList);
                                    // 本地要切换设备
                                    if (query?.planType === 2) {
                                        if (androidConfigForm.getFieldValue('deviceType') === 1) {
                                            androidConfigForm.setFieldValue(
                                                'deviceType',
                                                isElectron() ? 3 : 2
                                            );
                                        }
                                        if (iosConfigForm.getFieldValue('deviceType') === 1) {
                                            iosConfigForm.setFieldValue(
                                                'deviceType',
                                                isElectron() ? 3 : 2
                                            );
                                        }
                                    }
                                }}
                            />
                            {/* 如果是更新配置，不支持从模板导入 */}
                            {/* {!query?.planId && (
                                <Popconfirm
                                    placement="right"
                                    title={'该操作会覆盖当前已选择的数据，请谨慎操作'}
                                    // width={300}
                                    // disabled
                                    destroyTooltipOnHide
                                    // zIndexPopup={1}
                                    style={{ width: 800 }}
                                    description={
                                        <SelectWithDropdownRender
                                            style={{
                                                flex: 1,
                                                margin: '10px 0',
                                                width: 300
                                            }}
                                            placeholder="请选择模板"
                                            text="计划模板选择"
                                            disabled={query?.planId}
                                            showSearchIcon={false}
                                            options={templateList
                                                .filter((item) =>
                                                    filteredList.some(
                                                        (filterItem) =>
                                                            filterItem.groupId === item.groupId
                                                    )
                                                )
                                                .map((item) => ({
                                                    value: item.templateId,
                                                    label: item.templateName
                                                }))}
                                            filterOption={(input, option) =>
                                                option.label.includes(input)
                                            }
                                            showSearch
                                            value={currentTemplateId}
                                            onChange={(value) => {
                                                setCurrentTemplateId(value);
                                            }}
                                            onClick={() => {
                                                fetchTemplates();
                                            }}
                                            addChange={() => {
                                                creatTemplateModalRef.current.show();
                                            }}
                                            settingChange={() => {
                                                templateManagerRef.current?.show({
                                                    key: 'plan-template',
                                                    label: '计划模板',
                                                    path: '/system-setting/plan-template'
                                                });
                                            }}
                                            allowClear
                                        />
                                    }
                                    okText="确定"
                                    cancelText="取消"
                                    onConfirm={async () => {
                                        try {
                                            // 清空当前的表单数据
                                            commonConfigForm.resetFields();
                                            executeConfigForm.resetFields();
                                            serverAlarmForm.resetFields();

                                            const currentTemplateDetail =
                                                await getPlanTemplateDetail({
                                                    templateId: currentTemplateId
                                                });

                                            setGroupId(currentTemplateDetail?.groupId);
                                            getTreeNodeListWithPlanType(
                                                currentTemplateDetail?.groupId,
                                                groupList
                                            );

                                            const templateConfig =
                                                currentTemplateDetail.templateConfig;
                                            const selectedNodes = templateConfig.treeNodeIdList.map(
                                                (node) => `${node.treeNodeId}-${node.osType}`
                                            );

                                            // 设置计划名称
                                            commonConfigForm.setFieldsValue({
                                                name: templateConfig.name,
                                                nodeTree: selectedNodes,
                                                executeType: 1
                                            });
                                            setSelectedNode(selectedNodes);

                                            // 设置自动化配置
                                            const serverParam = templateConfig?.cloudParams?.find(
                                                (p) => p.type === 4
                                            );
                                            if (serverParam) {
                                                executeConfigForm.setFieldsValue({
                                                    cloudDevice: serverParam?.poolList?.[0],
                                                    cloudRetry: serverParam?.retryTimes > 0,
                                                    cloudRetryTimes: serverParam?.retryTimes || 3
                                                });

                                                serverAlarmForm.setFieldsValue({
                                                    toid: serverParam?.alarmInfo?.toid,
                                                    webhook: serverParam?.alarmInfo?.webhook,
                                                    statusList: serverParam?.alarmInfo?.statusList,
                                                    atuseridName:
                                                        serverParam?.alarmInfo?.atuseridName
                                                });

                                                // 更新环境参数
                                                setCreatePlanOption((prev) => ({
                                                    ...prev,
                                                    executeConfig: {
                                                        ...prev.executeConfig,
                                                        server: {
                                                            ...prev.executeConfig.server,
                                                            envParams: serverParam?.envParams || {}
                                                        }
                                                    }
                                                }));
                                            }

                                            setCaseNodeList(
                                                templateConfig?.treeNodeIdList?.map((item) => {
                                                    return {
                                                        ...item,
                                                        nodeId: item.treeNodeId,
                                                        caseIdList: item.caseNodeList
                                                    };
                                                })
                                            );
                                        } catch (error) {
                                            console.error(error);
                                            messageApi.error('加载模板失败，请重试');
                                        }
                                    }}
                                >
                                    <Button type="link">从模板快速导入配置</Button>
                                </Popconfirm>
                            )} */}
                        </CardTitle>
                        <div className={styles.cardLayout}>
                            <Form.Item
                                name="nodeTree"
                                shouldUpdate={() => false}
                                required
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (selectedNode && selectedNode.length > 0) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(
                                                new Error('请选择需要测试的用例')
                                            );
                                        }
                                    })
                                ]}
                            >
                                <div className={styles.nodeTree}>
                                    <Search
                                        className={styles.search}
                                        value={searchValue}
                                        placeholder="请输入要检索的内容"
                                        onChange={(e) => onSearchValueChange(e)}
                                    />
                                    <Spin spinning={loading}>
                                        <div
                                            className={styles.tree}
                                            style={{ maxHeight: window.innerHeight - 400 }}
                                        >
                                            {treeData?.length ? (
                                                <Tree
                                                    onExpand={onExpand}
                                                    checkable
                                                    showLine
                                                    autoExpandParent={autoExpandParent}
                                                    expandedKeys={expandedKeys}
                                                    treeData={treeData}
                                                    checkedKeys={selectedNode}
                                                    onCheck={(
                                                        keys,
                                                        { checkedNodes, halfCheckedKeys }
                                                    ) => {
                                                        const selectKes = [
                                                            ...checkedNodes.map((item) => item.key)
                                                        ];
                                                        setSelectNodeOrhalfCheckedKeys([
                                                            ...selectKes,
                                                            ...halfCheckedKeys
                                                        ]);
                                                        setSelectedNode(selectKes);
                                                        let newCaseNodeList = [];
                                                        keys = keys.filter((item) =>
                                                            (item + '').includes('-')
                                                        );
                                                        for (let item of caseNodeList) {
                                                            if (
                                                                keys.includes(
                                                                    item.nodeId + '-' + item.osType
                                                                )
                                                            ) {
                                                                newCaseNodeList.push(item);
                                                            }
                                                        }
                                                        setCaseNodeList(newCaseNodeList);
                                                        commonConfigForm.setFieldValue('nodeTree', [
                                                            ...checkedNodes.map((item) => item.key)
                                                        ]);
                                                    }}
                                                />
                                            ) : (
                                                <NoContent
                                                    text="暂无集成回归用例"
                                                    className={styles.noContent}
                                                />
                                            )}
                                        </div>
                                    </Spin>
                                </div>
                            </Form.Item>
                        </div>
                        {/* Add the rest of the task info form items here */}
                    </Form>
                </div>
            )
        },
        {
            title: '执行配置',
            content: (
                <div>
                    <div className={styles.cardLayout}>
                        <Form
                            key="executeConfig"
                            form={executeConfigForm}
                            layout="vertical"
                            requiredMark={false}
                            colon={false}
                        >
                            <Form.Item
                                label="执行器集群"
                                name="poolId"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择执行器集群'
                                    }
                                ]}
                            >
                                <Select
                                    placeholder="请选择执行器集群"
                                    options={poolList.map((item) => ({
                                        value: item.poolId,
                                        label: item.poolName
                                    }))}
                                />
                            </Form.Item>
                            <ServerEnvParams
                                form={executeConfigForm}
                                osType={4}
                                envList={envList?.server ?? []}
                                createPlanOption={createPlanOption}
                                setCreatePlanOption={(res) => {
                                    setCreatePlanOption(res);
                                }}
                            />
                            <div
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '5px',
                                    height: '30px',
                                    marginTop: '30px'
                                }}
                            >
                                <Form.Item
                                    name="cloudRetry"
                                    valuePropName="checked"
                                    noStyle
                                    tooltip={{
                                        title: '单计划执行失败后自动重试的最大次数，范围：0-3',
                                        iconRender: () => null
                                    }}
                                >
                                    <div>
                                        <Checkbox>是否重试</Checkbox>
                                        <Tooltip title="单计划执行失败后自动重试的最大次数，范围：0-3">
                                            <InfoCircleOutlined
                                                style={{ color: 'grey', marginRight: '25px' }}
                                            />
                                        </Tooltip>
                                    </div>
                                </Form.Item>

                                <Form.Item
                                    noStyle
                                    shouldUpdate={(prevValues, currentValues) =>
                                        prevValues.cloudRetry !== currentValues.cloudRetry
                                    }
                                >
                                    {({ getFieldValue }) => {
                                        return getFieldValue('cloudRetry') ? (
                                            <Form.Item name="retryTimes" initialValue={3} noStyle>
                                                <InputNumber
                                                    min={0}
                                                    max={3}
                                                    placeholder="重试次数"
                                                    style={{ width: 120 }}
                                                    addonAfter="次"
                                                />
                                            </Form.Item>
                                        ) : null;
                                    }}
                                </Form.Item>
                            </div>
                        </Form>
                    </div>
                </div>
            )
        },
        {
            title: '报警策略',
            content: (
                <>
                    <AlarmConfigForm key="serverAlarm" form={serverAlarmForm} />
                </>
            )
        }
    ];

    return (
        <Spin spinning={createLoading}>
            <CardContent>
                {contextHolder}
                <Descriptions column={1} size="small" />
                <div className={styles.header}>
                    <CardHeader
                        text={query?.planTemplateId ? '更新计划模板' : '新建计划模板'}
                        extra={
                            <>
                                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    {currentStep > 0 && (
                                        <Button
                                            style={{ margin: '0 8px' }}
                                            onClick={() => setCurrentStep(currentStep - 1)}
                                        >
                                            上一步
                                        </Button>
                                    )}
                                    {currentStep < steps.length - 1 && (
                                        <Button
                                            type="primary"
                                            onClick={async () => {
                                                try {
                                                    if (currentStep === 0) {
                                                        await commonConfigForm.validateFields();
                                                        const commonConfigFormValues =
                                                            await commonConfigForm.getFieldsValue();
                                                        setFirstStepData(commonConfigFormValues);
                                                    }
                                                    if (currentStep === 1) {
                                                        await executeConfigForm?.validateFields();
                                                        setSecondsStepData({
                                                            executeConfig:
                                                                executeConfigForm?.getFieldsValue()
                                                        });
                                                    }
                                                    setCurrentStep(currentStep + 1);
                                                } catch (error) {
                                                    console.log(error, 'error');
                                                    messageApi.error('请检查表单填写是否正确');
                                                }
                                            }}
                                        >
                                            下一步
                                        </Button>
                                    )}
                                    {currentStep === steps.length - 1 && (
                                        <div>
                                            {query?.planTemplateId ? (
                                                <Popconfirm
                                                    placement="right"
                                                    title="您确定要更新测试计划的配置？"
                                                    description="特别注意: 若取消勾选的用例将清除所有执行数据"
                                                    onConfirm={onSubmit}
                                                    cancelText="取消"
                                                    okText="确认"
                                                >
                                                    <Button type="primary">更新</Button>
                                                </Popconfirm>
                                            ) : (
                                                <Button type="primary" onClick={onSubmit}>
                                                    创建
                                                </Button>
                                            )}
                                        </div>
                                    )}
                                    <Button
                                        onClick={() => {
                                            navigate(-1);
                                        }}
                                    >
                                        取消
                                    </Button>
                                </div>
                            </>
                        }
                    />
                </div>
                <CaseNodeTree
                    ref={caseNodeTreeModalRef}
                    caseNodeList={caseNodeList}
                    modalType={query?.planId ? 'edit' : 'create'}
                    setCaseNodeList={(e, isInit) => {
                        setCaseNodeList(e);
                        // 非初始化加载节点
                        if (!isInit) {
                            // 判断是否被改过
                            const changeMap = {};
                            e?.forEach((v) => {
                                const id = `${v.nodeId}-${v.osType}`;
                                // 长度不一致且有caseIdList 且不为空数组，则为被修改过
                                const originData = nodeCaseKesMap[id]?.sort((a, b) => a - b);
                                const newData = (v.caseIdList || []).sort((a, b) => a - b);
                                if (
                                    JSON.stringify(originData) !== JSON.stringify(newData) &&
                                    v.caseIdList?.length > 0
                                ) {
                                    changeMap[id] = true;
                                } else {
                                    changeMap[id] = false;
                                }
                            });
                            setNodeCaseIsChangeMap(changeMap);
                        }
                    }}
                    setCaseNodeKeys={(nodeKeys, nodeId) => {
                        // 记录当前节点的所有case叶子结点
                        setNodeCaseKesMap((res) => {
                            return {
                                ...res,
                                [nodeId]: nodeKeys
                            };
                        });
                    }}
                />
                <Steps current={currentStep} style={{ marginTop: 50, marginBottom: 20 }}>
                    {steps?.map((item) => (
                        <Steps.Step key={item.title} title={item.title} />
                    ))}
                </Steps>
                <div className={styles.stepsContent}>{steps[currentStep].content}</div>
            </CardContent>
            <CreatTemplateModal ref={creatTemplateModalRef} />
            <SettingModal ref={templateManagerRef} />
        </Spin>
    );
}

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule,
    planList: state.common.plan.planList,
    planTemplateList: state.common.plan.planTemplateList,
    currentPlanGroup: state.common.plan.currentPlanGroup,
    serverList: state.common.case.serverList,
    currentPlan: state.common.plan.currentPlan
}))(CreateGroupPage);
