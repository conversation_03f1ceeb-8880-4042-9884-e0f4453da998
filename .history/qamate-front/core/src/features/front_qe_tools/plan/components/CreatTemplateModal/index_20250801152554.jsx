import { useEffect, useState, useMemo, useRef, forwardRef, useImperativeHandle } from 'react';
import { useNavigate, useLocation } from 'umi';
import {
    Button,
    Form,
    Input,
    message,
    Modal,
    Popconfirm,
    Tabs,
    Tag,
    Tree,
    Spin,
    Tooltip,
    Badge,
    Select,
    Switch,
    Radio
} from 'antd';
import {
    AndroidOutlined,
    AppleOutlined,
    DownOutlined,
    FilterOutlined,
    CloudServerOutlined
} from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardTitle } from 'COMMON/components/common/Card';
import NoContent from 'COMMON/components/common/NoContent';
import NodeIcon from 'COMMON/components/NewAddDropdown/components/NodeIcon';
import RenderTitle from 'FEATURES/front_qe_tools/plan/components/RenderTitle';
import {
    getNewIpRedirect,
    getNewIpRedirectReverse
} from 'COMMON/components/TreeComponents/Step/utils';
import {
    updatePlan,
    getPlanDetail,
    getDevicePoolList,
    createPlanTemplate,
    updatePlanTemplate,
    getPlanTemplateDetail
} from 'COMMON/api/front_qe_tools/plan/plan';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';
import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import CloudDevice from 'COMMON/components/execution/CloudDevice';
import InstallParams from 'COMMON/components/execution/InstallParams';
import { planTypeOptions } from 'COMMON/utils/planUtils.js';
import NotificationConfig from '../Setting/NotificationConfig';
import IpRedirect from '../Setting/IpRedirect';
import RetryTimes from '../Setting/RetryTimes';
import LocalDevice from '../Setting/LocalDevice';
import DeviceType from '../Setting/DeviceType';
import SystemPop from '../Setting/SystemPop';
import LogCheck from '../Setting/LogCheck';
import LogCat from '../Setting/LogCat';
import EnvParams from '../Setting/EnvParams';
import CaseNodeTree from '../Setting/CaseNodeTree';
import styles from './index.module.less';

const { Search } = Input;

// 扁平树为数组
function flattenTree(node, parentId = null, flattenedTree = []) {
    const nodeCopy = deepcopy(node);
    nodeCopy.parentId = parentId;
    flattenedTree.push(nodeCopy);

    const children = node.children || [];
    for (const child of children) {
        flattenTree(child, node.nodeId, flattenedTree);
    }

    return flattenedTree;
}
// 从树结构中提取选中的节点，并生成包含任务的节点ID列表
function getSelectedNodeKeys(tree, treeNodeList, nodeList = []) {
    tree.forEach((item) => {
        if (item.nodeType !== 1) {
            let node = treeNodeList.find((v) => v.treeNodeId === item.nodeId);
            if (node) {
                node?.taskList?.forEach((task) => {
                    nodeList.push(item.nodeId + '-' + task.osType);
                });
            }
        } else {
            getSelectedNodeKeys(item.children, treeNodeList, nodeList);
        }
    });
    return nodeList;
}

const CreateTemplateModal = forwardRef((props, ref) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    // const [createLoading, setCreateLoading] = useState(false);

    const showModal = async () => {
        handleCloseModal();
        setIsModalVisible(true);
        const { poolList } = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 3
            // 1-Android 2-iOS 3-Server
        });
        setPoolList(poolList);
    };

    const handleOk = () => {
        // 确认后逻辑
        setIsModalVisible(false);
    };

    const handleCloseModal = () => {
        serverConfigForm.resetFields();
        androidConfigForm.resetFields();
        iosConfigForm.resetFields();
        // 清空选中的节点
        setSelectedNode([]);
        setIsModalVisible(false);
        setIsModalVisible(false);
        setTemplateId(null);
        setServerShowMore(false);
        setIosShowMore(false);
        setAndroidShowMore(false);
    };
    const { currentSpace, planList, fetchTemplates } = props;
    const query = getQueryParams();
    const location = useLocation();
    const [loading, setLoading] = useState(true);
    const [planType, setPlanType] = useState();
    const [createLoading, setCreateLoading] = useState(false);
    const [selectedNode, setSelectedNode] = useState([]);
    const [caseNodeList, setCaseNodeList] = useState([]);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [nodeTreeMapList, setNodeTreeMapList] = useState([]);
    const [groupList, setGroupList] = useState([]);
    const [groupId, setGroupId] = useState(null);
    // const [showMore, setShowMore] = useState(false);
    const [serverShowMore, setServerShowMore] = useState(false);
    const [iosShowMore, setIosShowMore] = useState(false);
    const [androidShowMore, setAndroidShowMore] = useState(false);
    const [filteredList, setFilteredList] = useState([]);
    const [cloudDeviceList, setCloudDeviceList] = useState({}); // 双端云设备列表
    const [envList, setEnvList] = useState([]); // 环境列表
    const [showOtherConfig, setShowOtherConfig] = useState(false); // 是否显示其他配置
    // const [activeExecuteConfigKey, setActiveExecuteConfigKey] = useState('android');
    const [activeExecuteConfigKey, setActiveExecuteConfigKey] = useState('android');
    const caseNodeTreeModalRef = useRef();
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [commonConfigForm] = Form.useForm();
    const [androidConfigForm] = Form.useForm();
    const [serverConfigForm] = Form.useForm();
    const [iosConfigForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    // const [creationMethod, setCreationMethod] = useState('custom');
    const [creationMethod, setCreationMethod] = useState('custom');
    const [poolList, setPoolList] = useState([]); // 设备池列表
    const [templateList, setTemplateList] = useState([]); // 模板列表
    const [templateId, setTemplateId] = useState(null);
    const navigate = useNavigate();
    useImperativeHandle(ref, () => ({
        show: (id) => {
            showModal();

            if (id) {
                setTemplateId(id); // 接收传入的 templateId 并保存
            } else {
                setTemplateId(null);
                commonConfigForm.setFieldsValue({
                    templateName: null,
                    nodeTree: null
                });
                // 清除安卓、iOS和服务器配置表单值
                androidConfigForm.setFieldsValue({
                    deviceType: 2,
                    retryTimes: 0
                });
                iosConfigForm.setFieldsValue({
                    deviceType: 2,
                    retryTimes: 0
                });
                // serverConfigForm.setFieldsValue({
                //     deviceType: 2
                // });
                setCaseNodeList([]);
            }
        }
    }));
    useEffect(() => {
        if (!templateId) {
            return;
        }
        const fetchTemplateDetails = async () => {
            try {
                const res = await getPlanTemplateDetail({
                    templateId: templateId
                });
                // console.log('API Response:', res);

                // 如果res不存在，提前返回
                if (!res) {
                    console.warn('No data returned from API');
                    return;
                }

                const { templateName, templateConfig } = res;
                const { cloudParams, treeNodeIdList } = templateConfig || {};
                const selectNodes = treeNodeIdList
                    ? treeNodeIdList.map((node) => `${node.treeNodeId}-${node.osType}`)
                    : [];
                // 设置计划名称
                commonConfigForm.setFieldsValue({
                    templateName: templateName,
                    nodeTree: selectNodes
                });

                setSelectedNode(selectNodes);

                // 查找安卓、iOS和服务器配置
                const androidConfig = cloudParams
                    ? cloudParams.find((item) => item.type === 1)
                    : null;
                const iosConfig = cloudParams ? cloudParams.find((item) => item.type === 2) : null;
                const serverConfig = cloudParams
                    ? cloudParams.find((item) => item.type === 4)
                    : null;

                // 设置安卓配置表单值
                if (androidConfig) {
                    // Android
                    androidConfigForm.setFieldsValue({
                        localDevice: androidConfig?.deviceIdList,
                        cloudDevice: androidConfig?.poolList,
                        sysAlertClear: androidConfig?.sysAlertClear,
                        logCollect: androidConfig?.logCollect.needLog,
                        filter: androidConfig?.logCollect.filter,
                        logcheckInfo: androidConfig?.logcheckInfo?.needLogCheck,
                        cuid: androidConfig?.logcheckInfo?.cuid,
                        installParams: androidConfig?.installParams,
                        toid: androidConfig?.alarmInfo?.toid
                            ? +androidConfig?.alarmInfo?.toid
                            : null,
                        webhook: androidConfig?.alarmInfo?.webhook,
                        statusList: androidConfig?.alarmInfo?.statusList,
                        atuseridName: androidConfig?.alarmInfo?.atuseridName,
                        envParams: androidConfig.envParams,
                        deviceType:
                            androidConfig?.deviceIdList?.length > 0
                                ? 3
                                : androidConfig?.poolList?.length > 0
                                ? 1
                                : 2,
                        retryTimes: androidConfig.retryTimes || 0
                    });
                    if (androidConfig?.alarmInfo?.toid) {
                        setAndroidShowMore(true);
                    } else {
                        setAndroidShowMore(false);
                    }
                }

                // 设置iOS配置表单值
                if (iosConfig) {
                    iosConfigForm.setFieldsValue({
                        localDevice: iosConfig?.deviceIdList,
                        cloudDevice: iosConfig.poolList,
                        sysAlertClear: iosConfig.sysAlertClear,
                        logCollect: iosConfig?.logCollect?.needLog,
                        filter: iosConfig?.logCollect?.filter,
                        logcheckInfo: iosConfig?.logcheckInfo?.needLogCheck,
                        cuid: iosConfig?.logcheckInfo?.cuid,
                        installParams: iosConfig?.installParams,
                        toid: iosConfig?.alarmInfo?.toid ? +iosConfig?.alarmInfo?.toid : null,
                        webhook: iosConfig?.alarmInfo?.webhook,
                        statusList: iosConfig?.alarmInfo?.statusList,
                        atuseridName: iosConfig?.alarmInfo?.atuseridName,
                        envParams: iosConfig?.envParams,
                        deviceType:
                            iosConfig?.deviceIdList?.length > 0
                                ? 3
                                : iosConfig?.poolList?.length > 0
                                ? 1
                                : 2,
                        retryTimes: iosConfig.retryTimes || 0
                    });
                    if (iosConfig?.alarmInfo?.toid) {
                        setIosShowMore(true);
                    } else {
                        setIosShowMore(false);
                    }
                }

                // 设置服务器配置表单值
                if (serverConfig) {
                    serverConfigForm.setFieldsValue({
                        envParams: serverConfig?.envParams, // 根据需要调整结构
                        toid: serverConfig?.alarmInfo?.toid ? +serverConfig?.alarmInfo?.toid : null,
                        webhook: serverConfig?.alarmInfo?.webhook,
                        statusList: serverConfig?.alarmInfo?.statusList,
                        atuseridName: serverConfig?.alarmInfo?.atuseridName,
                        poolId: serverConfig?.poolList ? serverConfig?.poolList[0] : null,
                        deviceType: serverConfig?.poolList?.length > 0 ? 1 : 2,
                        modeType: serverConfig?.modeType || 0,
                        stableEnvironmentId:
                            serverConfig?.modeConfig?.serverConfig?.stableEnvironmentId,
                        testEnvironmentId:
                            serverConfig?.modeConfig?.serverConfig?.testEnvironmentId,
                        jsonSchemaCheck: serverConfig?.modeConfig?.assertConfig?.jsonSchemaCheck,
                        intelligentNoiseReduce:
                            serverConfig?.modeConfig?.assertConfig?.intelligentNoiseReduce
                    });
                    if (serverConfig?.alarmInfo?.toid) {
                        setServerShowMore(true);
                    } else {
                        setServerShowMore(false);
                    }
                }

                // 更新执行配置
                setCreatePlanOption((prev) => ({
                    ...prev,
                    executeConfig: {
                        android: {
                            ...prev?.executeConfig?.android,
                            ...androidConfig,
                            ipRedirect:
                                getNewIpRedirectReverse(androidConfig?.requestDirectMap) || []
                        },
                        ios: {
                            ...prev?.executeConfig?.ios,
                            ...iosConfig,
                            ipRedirect: getNewIpRedirectReverse(iosConfig?.requestDirectMap) || []
                        },
                        server: {
                            ...prev?.executeConfig?.server,
                            ...serverConfig
                        }
                    }
                }));
                setCaseNodeList(
                    templateConfig.treeNodeIdList.map((item) => {
                        return {
                            ...item,
                            nodeId: item.treeNodeId,
                            caseIdList: item.caseNodeList
                        };
                    })
                );
            } catch (error) {}
        };
        fetchTemplateDetails();
    }, [
        templateId,
        commonConfigForm,
        androidConfigForm,
        iosConfigForm,
        serverConfigForm,
        setCreatePlanOption
    ]);
    const getNodeList = (nodeTree, nodeList = []) => {
        nodeTree.forEach((item) => {
            if (!item?.children || item.children.length === 0) {
                nodeList.push(item.nodeId);
            }
            if (item?.children && item.children.length > 0) {
                getNodeList(item.children, nodeList);
            }
        });
        return nodeList;
    };

    const getNewTree = (tree) => {
        return tree.map((item) => {
            if (item.nodeType === 2) {
                let _osTypeList;
                if (item.signType === 1) {
                    _osTypeList = [3];
                } else if (item.signType === 0 && item.osType === 3) {
                    _osTypeList = [1, 2];
                } else {
                    _osTypeList = [item.osType];
                }
                item.children = _osTypeList.map((os) => ({
                    nodeId: item.nodeId + '-' + os,
                    os: os,
                    caseRootId: item.caseRootId,
                    caseNodeList: []
                }));
                return item;
            } else {
                return { ...item, children: getNewTree(item.children) };
            }
        });
    };
    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            let filter = [];
            if (+query?.planType === 1) {
                // 集成回归测试情况下 展示集成回归用例组和本地执行用例组
                filter = groupList.filter(
                    (group) => +group.groupType === 2 || +group.groupType === 4
                );
            } else if (location.pathname.includes('daily')) {
                // 定时巡检测试情况下 展示集成回归用例组
                filter = groupList.filter((group) => +group.groupType === 2);
            } else {
                // 通用测试情况下 展示所有用例组
                filter = groupList;
            }
            setFilteredList(filter);
            setGroupId(filter[0]?.groupId);
            getTreeNodeListWithPlanType(filter[0]?.groupId, groupList);
            // getTreeNodeListWithPlanType(planType, groupList);
        }
        func();
    }, [currentSpace?.id, query?.planType]);
    const getTreeNodeListWithPlanType = async (groupId, groupList) => {
        try {
            setLoading(true);
            let group;
            //  集成回归用例组
            // if (planType === 1) {
            //     group = groupList.find((item) => item?.groupType === 2);
            // }
            // //  本地执行用例组
            // if (planType === 2) {
            //     group = groupList.find((item) => item?.groupType === 4);
            // }
            if (!groupId) {
                return;
            }
            // setGroupId(group?.groupId);
            let { tree } = await getTreeNodeList({ groupId: groupId });
            setDirectoryTreeData(getNewTree(tree));
            setSearchValue('');
            let nodeKeys = tree.map((item) => flattenTree(item)).flat() ?? [];
            setNodeTreeMapList(nodeKeys);
            // 获取 创建第一次进入默认全部key
            setExpandedKeys(nodeKeys.map((item) => item.nodeId));
            setLoading(false);
        } catch (err) {}
    };

    // 获取已创建用例的对应内容
    const getExecutePlanCaseNodeList = (tree, nodeList = []) => {
        tree.map((item) => {
            // 用例
            if (item.nodeType === 2 && item.caseNodeIdList) {
                for (let _item of item.caseNodeList) {
                    nodeList.push({
                        nodeId: item.nodeId,
                        caseRootId: item.caseRootId,
                        osType: _item.osType,
                        caseIdList: _item?.caseNodeIdList ?? []
                    });
                }
            }
            if (!isEmpty(item.children) && item.nodeType === 1) {
                getExecutePlanCaseNodeList(item.children);
            }
        });
        return nodeList;
    };
    useEffect(() => {
        if (query?.planId) {
            let currentPlanDetail = planList.find((item) => item?.id === +query?.planId);
            getPlanDetail({ planIdList: [+currentPlanDetail?.id] }).then((res) => {
                const planDetail = res.planList[0];
                let nodeList = getSelectedNodeKeys(
                    planDetail?.nodeTree ?? [],
                    planDetail?.treeNodeList
                );
                setSelectedNode(nodeList);
                let caseList = getExecutePlanCaseNodeList(planDetail?.nodeTree ?? []);
                setCaseNodeList(caseList);
                setExpandedKeys([
                    ...(planDetail?.nodeTree
                        ? expandedKeys.filter((item) => nodeList.includes(item?.nodeId))
                        : expandedKeys
                    ).map((item) => item?.nodeId)
                ]);
            });
        }
    }, [planList]);

    useEffect(() => {
        if (commonConfigForm && query?.planId) {
            let currentPlanDetail = planList.find((item) => item?.id === +query?.planId);
            getPlanDetail({ planIdList: [+currentPlanDetail?.id] }).then((res) => {
                const planDetail = res.planList[0];
                let nodeList = getSelectedNodeKeys(
                    planDetail?.nodeTree ?? [],
                    planDetail?.treeNodeList
                );
                commonConfigForm.setFieldValue('name', planDetail?.name ?? '');
                commonConfigForm.setFieldValue('nodeTree', nodeList ?? '');
            });
        }
    }, [commonConfigForm, planList]);

    useEffect(() => {
        if (androidConfigForm) {
            androidConfigForm.setFieldValue('deviceType', 2);
            androidConfigForm.setFieldValue('mockSwitch', true);
            const showMore = androidConfigForm?.getFieldValue('toid');
            if (isEmpty(showMore)) {
                setAndroidShowMore(false);
                return;
            }
            setAndroidShowMore(true);
        }
    }, [androidConfigForm]);

    useEffect(() => {
        if (iosConfigForm) {
            iosConfigForm.setFieldValue('deviceType', 2);
            iosConfigForm.setFieldValue('mockSwitch', true);
            const showMore = iosConfigForm?.getFieldValue('toid');
            if (isEmpty(showMore)) {
                setIosShowMore(false);
                return;
            }
            setIosShowMore(true);
        }
    }, [iosConfigForm]);
    useEffect(() => {
        if (serverConfigForm) {
            const serverShowMore = serverConfigForm?.getFieldValue('toid');
            if (isEmpty(serverShowMore)) {
                setServerShowMore(false);
                return;
            }
            setServerShowMore(true);
        }
    }, [serverConfigForm?.getFieldValue('toid')]);
    useEffect(() => {
        obtainCloudDeviceList();
        obtaionEnvParamsList();
    }, []);

    // 创建模板
    const onClick = async () => {
        // 校验表单
        try {
            await commonConfigForm?.validateFields();
            await androidConfigForm?.validateFields();
            await iosConfigForm?.validateFields();
            await serverConfigForm?.validateFields();
        } catch (e) {
            console.log(e);
            messageApi.warning('请填写完整表单');
            return;
        }
        try {
            const commonConfigFormValues = commonConfigForm?.getFieldsValue();
            let newNodeTree =
                commonConfigFormValues?.nodeTree?.filter((item) => ('' + item).includes('-')) || [];
            let treeNodeIdList = getTreeNodeIdList(
                directoryTreeData,
                [...new Set(newNodeTree?.map((item) => +(item + '').split('-')[0]))],
                newNodeTree
            );

            let planId;
            for (let item of treeNodeIdList) {
                let list = caseNodeList?.find(
                    (it) => it?.nodeId === item?.treeNodeId && it?.osType === item?.osType
                )?.caseIdList;
                if (list) {
                    item.caseNodeList = list;
                }
            }
            // return;
            setCreateLoading(true);
            if (query?.planId) {
                await updatePlan({
                    planId: query?.planId,
                    // templateId: query?.templateId,
                    templateName: commonConfigFormValues?.templateName,
                    templateConfig: {
                        moduleId: currentSpace?.id,
                        planType: query?.planType ?? 1,
                        treeNodeIdList: treeNodeIdList
                    }
                });
                planId = query?.planId;
            } else if (!query?.planId) {
                let params = {
                    moduleId: currentSpace?.id,
                    templateName: commonConfigFormValues.templateName,
                    groupId: groupId
                    // treeNodeIdList: treeNodeIdList
                };
                params.templateConfig = {
                    moduleId: currentSpace?.id,
                    name: commonConfigFormValues.templateName,
                    treeNodeIdList: treeNodeIdList
                };
                const androidConfigFormValues = androidConfigForm.getFieldsValue();
                const iosConfigFormValues = iosConfigForm.getFieldsValue();
                const serverConfigFormValues = serverConfigForm.getFieldsValue();
                let cloudParams = [];
                // 若有自动化配置
                if (androidConfigFormValues.deviceType !== 2) {
                    let androidParmas = {
                        type: 1,
                        retryTimes: androidConfigFormValues.retryTimes ?? 0,

                        sysAlertClear: androidConfigFormValues.sysAlertClear ?? true,
                        logCollect: {
                            needLog: androidConfigFormValues.logCollect ?? false,
                            filter: androidConfigFormValues.logCollect
                                ? androidConfigFormValues.filter
                                : ''
                        },
                        requestDirectMap: getNewIpRedirect(
                            createPlanOption?.executeConfig,
                            'android'
                        ).filter((item) => {
                            return (
                                item.oriAddress.hostname !== undefined &&
                                item.targetAddress.hostname !== undefined
                            );
                        }),
                        envParams: {
                            envDetail: {
                                envId: createPlanOption?.executeConfig?.android?.envParams?.envId,
                                paramList:
                                    createPlanOption?.executeConfig?.android?.envParams?.paramList.map(
                                        (item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue
                                        })
                                    ),
                                appList:
                                    createPlanOption?.executeConfig?.android?.envParams?.appList.map(
                                        (item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue
                                        })
                                    ),
                                serverList:
                                    createPlanOption?.executeConfig?.android?.envParams?.serverList.map(
                                        (item) => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue
                                        })
                                    )
                            },
                            alarmInfo: {
                                toid: androidConfigFormValues?.toid
                                    ? +androidConfigFormValues?.toid
                                    : null, // 群号
                                webhook: androidConfigFormValues?.webhook, // 机器人地址
                                atuseridName: androidConfigFormValues?.atuseridName, // 需@的人
                                statusList: androidConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                            }
                        }
                    };
                    // 仅本地设备参数 打点自动化校验
                    if (androidConfigFormValues.deviceType === 3) {
                        androidParmas.deviceIdList = androidConfigFormValues?.localDevice;
                        androidParmas.poolList = [0];
                        androidParmas.logcheckInfo = {
                            needLogCheck: androidConfigFormValues?.logcheckInfo ?? false,
                            cuid: androidConfigFormValues?.logcheckInfo
                                ? androidConfigFormValues?.cuid
                                : ''
                        };
                    }
                    // 仅云端设备参数 安装app
                    if (androidConfigFormValues.deviceType === 1) {
                        androidParmas.poolList = androidConfigFormValues.cloudDevice;
                        // name为空时，不传
                        androidParmas.installParams = (
                            androidConfigFormValues?.installParams ?? []
                        ).map((item) => {
                            const { name, ...rest } = item;
                            return name ? { name, ...rest } : { ...rest };
                        });
                    }
                    cloudParams.push(androidParmas);
                }
                if (iosConfigFormValues.deviceType !== 2) {
                    let iosParmas = {
                        type: 2,
                        retryTimes: iosConfigFormValues.retryTimes ?? 0,
                        sysAlertClear: iosConfigFormValues.sysAlertClear ?? true,
                        requestDirectMap: getNewIpRedirect(
                            createPlanOption?.executeConfig,
                            'ios'
                        ).filter((item) => {
                            return (
                                item.oriAddress.hostname !== undefined &&
                                item.targetAddress.hostname !== undefined
                            );
                        }),
                        envParams: {
                            envDetail: {
                                envId: createPlanOption?.executeConfig?.ios?.envParams?.envId,
                                paramList:
                                    createPlanOption?.executeConfig?.ios?.envParams?.paramList.map(
                                        (item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue
                                        })
                                    ),
                                appList:
                                    createPlanOption?.executeConfig?.ios?.envParams?.appList.map(
                                        (item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue
                                        })
                                    ),
                                serverList:
                                    createPlanOption?.executeConfig?.ios?.envParams?.serverList.map(
                                        (item) => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue
                                        })
                                    )
                            }
                        },
                        alarmInfo: {
                            toid: iosConfigFormValues?.toid ? +iosConfigFormValues?.toid : null, // 群号
                            webhook: iosConfigFormValues?.webhook, // 机器人地址
                            atuseridName: iosConfigFormValues?.atuseridName, // 需@的人
                            statusList: iosConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                        }
                    };
                    // 仅本地设备参数 打点自动化校验
                    if (iosConfigFormValues.deviceType === 3) {
                        iosParmas.deviceIdList = iosConfigFormValues.localDevice;
                        iosParmas.poolList = [0];
                        iosParmas.logcheckInfo = {
                            needLogCheck: iosConfigFormValues.logcheckInfo ?? false,
                            cuid: iosConfigFormValues.logcheckInfo ? iosConfigFormValues.cuid : ''
                        };
                        if (iosConfigFormValues.localDevice.length === 0) {
                            messageApi.error('设备获取失败，请重新选择');
                            return;
                        }
                    }
                    // 仅云端设备参数 安装app
                    if (iosConfigFormValues.deviceType === 1) {
                        iosParmas.poolList = iosConfigFormValues.cloudDevice;
                        // name 字段为空时，不传
                        iosParmas.installParams = (iosConfigFormValues?.installParams ?? []).map(
                            (item) => {
                                const { name, ...rest } = item;
                                return name ? { name, ...rest } : { ...rest };
                            }
                        );
                        if (iosConfigFormValues.cloudDevice.length === 0) {
                            messageApi.error('设备池获取失败，请重新选择');
                            return;
                        }
                    }
                    cloudParams.push(iosParmas);
                }
                // server配置
                if (serverConfigFormValues.deviceType === 1) {
                    let serverParmas = {
                        type: 4,
                        // retryTimes: androidConfigFormValues.retryTimes ?? 0,

                        // sysAlertClear: androidConfigFormValues.sysAlertClear ?? true,
                        // logCollect: {
                        //     needLog: androidConfigFormValues.logCollect ?? false,
                        //     filter: androidConfigFormValues.logCollect ? androidConfigFormValues.filter : ''
                        // },
                        // requestDirectMap: getNewIpRedirect(createPlanOption).filter(item => {
                        //     return item.oriAddress.hostname !== undefined && item.targetAddress.host !== undefined;
                        // }),
                        modeType: serverConfigFormValues.modeType ?? 1, // 模式类型: int: 1:普通模式（默认）；2:diff模式。
                        modeConfig: {
                            // 模式配置:
                            serverConfig: {
                                //  Server 配置
                                stableEnvironmentId: serverConfigFormValues?.stableEnvironmentId, // 稳定版环境id
                                testEnvironmentId: serverConfigFormValues?.testEnvironmentId // 待测版环境id
                            },
                            assertConfig: {
                                // 断言配置
                                jsonSchemaCheck: serverConfigFormValues.jsonSchemaCheck ?? false, // JSON Schema 校验
                                intelligentNoiseReduce:
                                    serverConfigFormValues.intelligentNoiseReduce ?? false // 智能去噪
                            }
                        },
                        poolList: [serverConfigFormValues.poolId],
                        envParams: {
                            envId: createPlanOption?.executeConfig?.server?.envParams?.envId,
                            envDetail: {
                                envId: createPlanOption?.executeConfig?.server?.envParams?.envId,
                                paramList:
                                    createPlanOption?.executeConfig?.server?.envParams?.paramList?.map(
                                        (item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue
                                        })
                                    ),
                                appList:
                                    createPlanOption?.executeConfig?.server?.envParams?.appList?.map(
                                        (item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue
                                        })
                                    ),
                                serverList:
                                    createPlanOption?.executeConfig?.server?.envParams?.serverList?.map(
                                        (item) => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue
                                        })
                                    )
                            }
                        },
                        // 报警
                        alarmInfo: {
                            toid: serverConfigFormValues?.toid
                                ? +serverConfigFormValues?.toid
                                : null, // 群号
                            webhook: serverConfigFormValues?.webhook, // 机器人地址
                            atuseridName: serverConfigFormValues?.atuseridName, // 需@的人
                            statusList: serverConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                        }
                    };
                    cloudParams.push(serverParmas);
                }
                // console.log(serverConfigFormValues, 'serverConfigFormValues');

                // cloudParams.push(serverConfigFormValues);
                if (!isEmpty(cloudParams)) {
                    params.templateConfig.cloudParams = cloudParams;
                }
                params.templateConfig.planType = +query?.planType ?? 1;
                // console.log(currentGroup, params, 'groupId');
                let planId = 0;
                // 调接口创建
                let res = {};
                if (templateId) {
                    res = await updatePlanTemplate({
                        ...params,
                        templateId: templateId
                    });
                    messageApi.success('更新成功');
                } else {
                    res = await createPlanTemplate(params);
                    messageApi.success('创建成功');
                }

                planId = res?.planId;
                if (typeof props?.onRefresh === 'function') {
                    props?.onRefresh();
                }

                // navigate(
                //     stringifyUrl({
                //         url: '/' + currentModule + '/group',
                //         query: {
                // moduleId: currentSpace?.id,
                //             planType: query?.planType ?? 1,
                //             planId: planId,
                //             'filters[planId]': query?.['filters[planId]']
                //         }
                //     })
                // );
                fetchTemplates && fetchTemplates();
                handleCloseModal();
                setCreateLoading(false);
            }
        } catch (err) {
            setCreateLoading(false);
        }
    };

    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };

    // 递归过滤隐藏菜单
    const getTreeNodeIdList = (tree = [], nodeList, nodeListWithOs, arr = []) => {
        if (!tree.length) {
            return [];
        }
        for (let item of tree) {
            // 若无子节点且不在list中，则不显示
            if (item.nodeType === 1) {
                getTreeNodeIdList(item?.children, nodeList, nodeListWithOs, arr);
            } else if (!nodeList.includes(item.nodeId)) {
                continue;
            } else if (nodeList.includes(item.nodeId)) {
                [1, 2, 3, 4, 5, 6].forEach((os) => {
                    if (nodeListWithOs.includes(item.nodeId + '-' + os)) {
                        arr.push({
                            treeNodeId: item?.nodeId,
                            osType: os
                        });
                    }
                });
            }
        }
        return arr;
    };

    // 获取云端设备池列表
    const obtainCloudDeviceList = async () => {
        if (!currentSpace?.id) {
            return;
        }
        let androidRes = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 1
        });
        let iosRes = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 2
        });
        setCloudDeviceList({
            android: androidRes?.poolList,
            ios: iosRes?.poolList
        });
    };

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        if (!currentSpace?.id) {
            return;
        }
        let androidRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 1
        });
        let iosRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 2
        });
        let serverRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 4
        });
        setEnvList({
            android: androidRes.envList,
            ios: iosRes.envList,
            server: serverRes?.envList
        });
    };

    // 递归过滤隐藏菜单
    const treeData = useMemo(() => {
        const loop = (data) => {
            let newData = [];
            data.forEach((item) => {
                const strTitle = !item?.nodeName || item?.nodeName === null ? '' : item.nodeName;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const title =
                    index > -1 ? (
                        <span>
                            {beforeStr}
                            <span className={styles.highlight}>{searchValue}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span>{strTitle}</span>
                    );
                if (item?.nodeType === 1) {
                    newData.push({
                        title: (
                            <>
                                <NodeIcon type={item.nodeType} />
                                {title}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId,
                        children: loop(item.children)
                    });
                } else if (item?.nodeType === 2) {
                    newData.push({
                        title: (
                            <>
                                <NodeIcon type={item.nodeType} />
                                {title}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId,
                        children: loop(item.children)
                    });
                } else if (item.os) {
                    let curNodeList =
                        caseNodeList.find(
                            (list) => list?.nodeId === item?.nodeId && item?.os === list?.osType
                        )?.caseIdList ?? [];
                    newData.push({
                        title: (
                            <>
                                <RenderTitle os={item.os} />
                                {selectedNode.includes(item.nodeId) && (
                                    <Tooltip title="筛选需要创建的用例, 默认全选" placement="right">
                                        <Badge dot={curNodeList?.length > 0}>
                                            <FilterOutlined
                                                className={styles.filter}
                                                onClick={() => {
                                                    caseNodeTreeModalRef?.current?.show(
                                                        item.caseRootId,
                                                        item.os,
                                                        +item.nodeId.split('-')?.[0]
                                                    );
                                                }}
                                            />
                                        </Badge>
                                    </Tooltip>
                                )}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId
                    });
                }
            });
            return newData;
        };
        return loop(directoryTreeData);
    }, [searchValue, directoryTreeData, selectedNode, caseNodeList]);

    const onSearchValueChange = (e) => {
        const { value } = e.target;
        const newExpandedKeys = nodeTreeMapList
            .map((item) => {
                if (`${item?.nodeName || ''}`.includes(value)) {
                    return item?.nodeId || null;
                }
                return null;
            })
            .filter((item) => item !== null);
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(true);
        setSearchValue(value);
    };

    const executeConfigItems = [
        {
            key: 'android',
            label: '安卓配置',
            icon: <AndroidOutlined />
        },
        {
            key: 'ios',
            label: 'iOS 配置',
            icon: <AppleOutlined />
        }
        // {
        //     key: 'server',
        //     label: '服务端配置',
        //     icon: <CloudServerOutlined />
        // }
    ];
    // const planTypeOptions = [
    //     {
    //         value: 1,
    //         label: '源于集成回归用例组'
    //     },
    //     {
    //         value: 2,
    //         disabled: +query?.planType === 5,
    //         label: '源于本地执行用例组'
    //     }
    // ];

    return (
        <>
            {contextHolder}
            <Modal
                open={isModalVisible}
                // onOk={handleOk}
                onCancel={handleCloseModal}
                title={templateId ? '更新模板配置' : '新增计划模板'}
                confirmLoading={createLoading}
                width={800}
                zIndex={99999}
                // height={'100%'}
                style={{ height: '700px', overflow: 'scroll' }}
                footer={[
                    <Button key="back" onClick={handleCloseModal}>
                        取消
                    </Button>,
                    <>
                        {templateId ? (
                            <Popconfirm
                                placement="right"
                                title="您确定要更新该模板的配置？"
                                // description="特别注意: 若取消勾选的用例将清除所有执行数据"
                                onConfirm={onClick}
                                cancelText="取消"
                                okText="确认"
                                zIndex={999999}
                            >
                                <Button type="primary">更新</Button>
                            </Popconfirm>
                        ) : (
                            <Button type="primary" onClick={onClick}>
                                创建
                            </Button>
                        )}
                    </>
                ]}
            >
                <di>
                    <Spin spinning={createLoading}>
                        <div>
                            <Form
                                form={commonConfigForm}
                                layout="vertical"
                                requiredMark={false}
                                colon={false}
                            >
                                <CardTitle text="计划名称" />
                                <div style={{ padding: '0 20px' }}>
                                    <Form.Item
                                        name="templateName"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请输入测试计划名称'
                                            }
                                        ]}
                                    >
                                        <Input placeholder="请输入内容" />
                                    </Form.Item>
                                </div>
                                <CardTitle text="测试用例">
                                    <Select
                                        variant="borderless"
                                        style={{
                                            width: +query?.planType !== 5 ? 161 : 145
                                        }}
                                        className={styles.selecPlanType}
                                        popupMatchSelectWidth={false}
                                        size="small"
                                        // value={planType}
                                        value={groupId}
                                        suffixIcon={
                                            +query?.planType !== 5 ? <DownOutlined /> : null
                                        }
                                        options={planTypeOptions(filteredList)}
                                        onChange={(value) => {
                                            // setPlanType(value);
                                            setGroupId(value);
                                            getTreeNodeListWithPlanType(value, groupList);
                                            // 本地要切换设备
                                            if (+query?.planType === 2) {
                                                if (
                                                    androidConfigForm.getFieldValue(
                                                        'deviceType'
                                                    ) === 1
                                                ) {
                                                    androidConfigForm.setFieldValue(
                                                        'deviceType',
                                                        isElectron() ? 3 : 2
                                                    );
                                                }
                                                if (
                                                    iosConfigForm.getFieldValue('deviceType') === 1
                                                ) {
                                                    iosConfigForm.setFieldValue(
                                                        'deviceType',
                                                        isElectron() ? 3 : 2
                                                    );
                                                }
                                            }
                                        }}
                                    />
                                </CardTitle>
                                <div className={styles.cardLayout}>
                                    <Form.Item
                                        name="nodeTree"
                                        shouldUpdate={() => false}
                                        required
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(_, value) {
                                                    if (selectedNode && selectedNode.length > 0) {
                                                        return Promise.resolve();
                                                    }
                                                    return Promise.reject(
                                                        new Error('请选择需要测试的用例')
                                                    );
                                                }
                                            })
                                        ]}
                                    >
                                        <div className={styles.nodeTree}>
                                            <Search
                                                className={styles.search}
                                                value={searchValue}
                                                placeholder="请输入要检索的内容"
                                                onChange={(e) => onSearchValueChange(e)}
                                            />
                                            <Spin spinning={loading}>
                                                <div
                                                    className={styles.tree}
                                                    style={{
                                                        maxHeight: window.innerHeight - 500
                                                    }}
                                                >
                                                    {treeData?.length ? (
                                                        <Tree
                                                            onExpand={onExpand}
                                                            checkable
                                                            showLine
                                                            autoExpandParent={autoExpandParent}
                                                            expandedKeys={expandedKeys}
                                                            treeData={treeData}
                                                            checkedKeys={selectedNode}
                                                            onCheck={(keys, { checkedNodes }) => {
                                                                setSelectedNode([
                                                                    ...checkedNodes.map(
                                                                        (item) => item.key
                                                                    )
                                                                ]);
                                                                let newCaseNodeList = [];
                                                                keys = keys.filter((item) =>
                                                                    (item + '').includes('-')
                                                                );
                                                                for (let item of caseNodeList) {
                                                                    if (
                                                                        keys.includes(
                                                                            item.nodeId +
                                                                                '-' +
                                                                                item.osType
                                                                        )
                                                                    ) {
                                                                        newCaseNodeList.push(item);
                                                                    }
                                                                }
                                                                setCaseNodeList(newCaseNodeList);
                                                                commonConfigForm.setFieldValue(
                                                                    'nodeTree',
                                                                    [
                                                                        ...checkedNodes.map(
                                                                            (item) => item.key
                                                                        )
                                                                    ]
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <NoContent
                                                            text="暂无集成回归用例"
                                                            className={styles.noContent}
                                                        />
                                                    )}
                                                </div>
                                            </Spin>
                                        </div>
                                    </Form.Item>
                                </div>
                            </Form>
                            {/* 小助手配置 */}
                            <>
                                <CardTitle text="自动化小助手配置" />
                                <div className={styles.cardLayout}>
                                    <Tabs
                                        type="card"
                                        items={executeConfigItems}
                                        activeKey={activeExecuteConfigKey}
                                        onChange={setActiveExecuteConfigKey}
                                    />
                                    <Form
                                        style={{
                                            display:
                                                activeExecuteConfigKey !== 'android'
                                                    ? 'none'
                                                    : 'block'
                                        }}
                                        form={androidConfigForm}
                                        layout="vertical"
                                        requiredMark={false}
                                        colon={false}
                                        initialValues={{
                                            statusList: [3, 4]
                                        }}
                                    >
                                        <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                                        {/* 是否使用设备选择 */}
                                        <DeviceType
                                            cloudDeviceList={cloudDeviceList?.android ?? []}
                                            form={androidConfigForm}
                                            planType={planType}
                                            osType={1}
                                        />
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) =>
                                                prevValues.deviceType !== currentValues.deviceType
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                return (
                                                    <>
                                                        {getFieldValue('deviceType') !== 2 && (
                                                            <>
                                                                {/* 具体设备选择 */}
                                                                {getFieldValue('deviceType') ===
                                                                1 ? (
                                                                    <CloudDevice
                                                                        form={androidConfigForm}
                                                                        cloudDeviceList={
                                                                            cloudDeviceList?.android ??
                                                                            []
                                                                        }
                                                                        osType={1}
                                                                    />
                                                                ) : (
                                                                    <LocalDevice
                                                                        form={androidConfigForm}
                                                                        osType={1}
                                                                    />
                                                                )}

                                                                {/* 执行重试次数 */}
                                                                <RetryTimes
                                                                    form={androidConfigForm}
                                                                />
                                                                <EnvParams
                                                                    form={androidConfigForm}
                                                                    osType={1}
                                                                    envList={envList?.android ?? []}
                                                                    createPlanOption={
                                                                        createPlanOption
                                                                    }
                                                                    setCreatePlanOption={
                                                                        setCreatePlanOption
                                                                    }
                                                                />
                                                                {/* 是否安装包 */}
                                                                {getFieldValue('deviceType') ===
                                                                    1 && (
                                                                    <InstallParams
                                                                        form={androidConfigForm}
                                                                    />
                                                                )}

                                                                <Form.Item
                                                                    label="其他配置"
                                                                    name="otherConfig"
                                                                    initialValue={0}
                                                                    layout="horizontal"
                                                                >
                                                                    <a
                                                                        style={{
                                                                            textDecoration:
                                                                                'underline'
                                                                        }}
                                                                        onClick={() => {
                                                                            androidConfigForm.setFieldValue(
                                                                                'otherConfig',
                                                                                !showOtherConfig
                                                                            );
                                                                            setShowOtherConfig(
                                                                                !showOtherConfig
                                                                            );
                                                                        }}
                                                                    >
                                                                        {showOtherConfig
                                                                            ? '收起'
                                                                            : '展开'}
                                                                    </a>
                                                                </Form.Item>
                                                                <div
                                                                    style={{
                                                                        display: showOtherConfig
                                                                            ? 'block'
                                                                            : 'none'
                                                                    }}
                                                                >
                                                                    {/* 是否使用系统弹窗 */}
                                                                    <SystemPop
                                                                        form={androidConfigForm}
                                                                    />
                                                                    {/* 是否打点上报 */}
                                                                    {getFieldValue('deviceType') ===
                                                                        3 && <LogCheck />}
                                                                    {/* 是否日志收集 */}
                                                                    <LogCat />
                                                                    {/* 日志转发配置 */}
                                                                    <IpRedirect
                                                                        createPlanOption={
                                                                            createPlanOption
                                                                        }
                                                                        setCreatePlanOption={
                                                                            setCreatePlanOption
                                                                        }
                                                                        isExcecuteConfig={true}
                                                                        osType={1}
                                                                    />
                                                                </div>
                                                                <NotificationConfig
                                                                    showMore={androidShowMore}
                                                                    setShowMore={setAndroidShowMore}
                                                                />
                                                            </>
                                                        )}
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                    </Form>
                                    <Form
                                        style={{
                                            display:
                                                activeExecuteConfigKey !== 'ios' ? 'none' : 'block'
                                        }}
                                        form={iosConfigForm}
                                        layout="vertical"
                                        requiredMark={false}
                                        colon={false}
                                        initialValues={{
                                            statusList: [3, 4]
                                        }}
                                    >
                                        {/* 是否使用设备选择 */}
                                        <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                                        <DeviceType
                                            cloudDeviceList={cloudDeviceList?.ios ?? []}
                                            form={iosConfigForm}
                                            planType={planType}
                                            osType={2}
                                        />
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) =>
                                                prevValues.deviceType !== currentValues.deviceType
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                return (
                                                    <>
                                                        {getFieldValue('deviceType') !== 2 && (
                                                            <>
                                                                {/* 具体设备选择 */}
                                                                {getFieldValue('deviceType') ===
                                                                1 ? (
                                                                    <CloudDevice
                                                                        form={iosConfigForm}
                                                                        cloudDeviceList={
                                                                            cloudDeviceList?.ios ??
                                                                            []
                                                                        }
                                                                        osType={2}
                                                                    />
                                                                ) : (
                                                                    <LocalDevice
                                                                        form={iosConfigForm}
                                                                        osType={2}
                                                                    />
                                                                )}
                                                                {/* 执行重试次数 */}
                                                                <RetryTimes form={iosConfigForm} />
                                                                <EnvParams
                                                                    form={iosConfigForm}
                                                                    osType={2}
                                                                    envList={envList?.ios ?? []}
                                                                    createPlanOption={
                                                                        createPlanOption
                                                                    }
                                                                    setCreatePlanOption={
                                                                        setCreatePlanOption
                                                                    }
                                                                />
                                                                {/* 是否安装包 */}
                                                                {getFieldValue('deviceType') ===
                                                                    1 && (
                                                                    <InstallParams
                                                                        form={iosConfigForm}
                                                                    />
                                                                )}

                                                                <Form.Item
                                                                    label="其他配置"
                                                                    name="otherConfig"
                                                                    initialValue={0}
                                                                    layout="horizontal"
                                                                >
                                                                    <a
                                                                        style={{
                                                                            textDecoration:
                                                                                'underline'
                                                                        }}
                                                                        onClick={() => {
                                                                            iosConfigForm.setFieldValue(
                                                                                'otherConfig',
                                                                                !showOtherConfig
                                                                            );
                                                                            setShowOtherConfig(
                                                                                !showOtherConfig
                                                                            );
                                                                        }}
                                                                    >
                                                                        {showOtherConfig
                                                                            ? '收起'
                                                                            : '展开'}
                                                                    </a>
                                                                </Form.Item>
                                                                <div
                                                                    style={{
                                                                        display: showOtherConfig
                                                                            ? 'block'
                                                                            : 'none'
                                                                    }}
                                                                >
                                                                    {/* 是否使用系统弹窗 */}
                                                                    <SystemPop
                                                                        form={iosConfigForm}
                                                                    />
                                                                    {/* 是否打点上报 */}
                                                                    {getFieldValue('deviceType') ===
                                                                        3 && <LogCheck />}
                                                                    {/* 是否日志收集 */}
                                                                    <LogCat />
                                                                    {/* 日志转发配置 */}
                                                                    <IpRedirect
                                                                        createPlanOption={
                                                                            createPlanOption
                                                                        }
                                                                        setCreatePlanOption={
                                                                            setCreatePlanOption
                                                                        }
                                                                        isExcecuteConfig={true}
                                                                        osType={2}
                                                                    />
                                                                </div>
                                                                <NotificationConfig
                                                                    showMore={iosShowMore}
                                                                    setShowMore={setIosShowMore}
                                                                />
                                                            </>
                                                        )}
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                    </Form>
                                    <Form
                                        style={{
                                            display:
                                                activeExecuteConfigKey === 'server'
                                                    ? 'block'
                                                    : 'none'
                                        }}
                                        initialValues={{
                                            statusList: [3, 4]
                                        }}
                                        form={serverConfigForm}
                                        layout="vertical"
                                        requiredMark={false}
                                        colon={false}
                                    >
                                        <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                                        <DeviceType
                                            cloudDeviceList={poolList ?? []}
                                            form={serverConfigForm}
                                            osType={4}
                                            // planType={planType}
                                        />

                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) =>
                                                prevValues.modeType !== currentValues.modeType ||
                                                prevValues.deviceType !== currentValues.deviceType
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                const modeType = getFieldValue('modeType');
                                                return (
                                                    <>
                                                        {getFieldValue('deviceType') !== 2 && (
                                                            <>
                                                                <Form.Item
                                                                    label="执行器集群"
                                                                    name="poolId"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择执行器集群"
                                                                        onClick={async () => {
                                                                            const { poolList } =
                                                                                await getDevicePoolList(
                                                                                    {
                                                                                        moduleId:
                                                                                            currentSpace?.id,
                                                                                        poolType: 3
                                                                                        // 1-Android 2-iOS 3-Server
                                                                                    }
                                                                                );
                                                                            setPoolList(poolList);
                                                                        }}
                                                                        options={poolList.map(
                                                                            (item) => ({
                                                                                value: item.poolId,
                                                                                label: item.poolName // 显示服务器名称和地址
                                                                            })
                                                                        )}
                                                                        allowClear
                                                                    />
                                                                </Form.Item>
                                                                <CardTitle
                                                                    text="执行模式"
                                                                    style={{ marginTop: 0 }}
                                                                />

                                                                <Form.Item
                                                                    // label="执行模式"
                                                                    name="modeType"
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                '请选择执行模式'
                                                                        }
                                                                    ]}
                                                                    initialValue={0}
                                                                >
                                                                    <Radio.Group>
                                                                        <Radio value={0}>
                                                                            普通模式
                                                                        </Radio>
                                                                        <Radio value={1}>
                                                                            Diff 模式
                                                                        </Radio>
                                                                    </Radio.Group>
                                                                </Form.Item>
                                                                {modeType === 0 ? (
                                                                    <EnvParams
                                                                        form={serverConfigForm}
                                                                        osType={4}
                                                                        envList={
                                                                            envList?.server ?? []
                                                                        }
                                                                        createPlanOption={
                                                                            createPlanOption
                                                                        }
                                                                        setCreatePlanOption={
                                                                            setCreatePlanOption
                                                                        }
                                                                    />
                                                                ) : null}
                                                                {modeType === 1 ? (
                                                                    <>
                                                                        <CardTitle text="Server 配置" />
                                                                        <Form.Item
                                                                            label="稳定版环境"
                                                                            name="stableEnvironmentId"
                                                                        >
                                                                            <Select
                                                                                placeholder="请选择稳定版环境"
                                                                                options={
                                                                                    envList?.server?.map(
                                                                                        (item) => {
                                                                                            return {
                                                                                                label: item.envName,
                                                                                                value: item.envId
                                                                                            };
                                                                                        }
                                                                                    ) ?? []
                                                                                }
                                                                                allowClear
                                                                            />
                                                                        </Form.Item>
                                                                        <Form.Item
                                                                            label="待测版环境"
                                                                            name="testEnvironmentId"
                                                                        >
                                                                            <Select
                                                                                placeholder="请选择待测版环境"
                                                                                options={
                                                                                    envList?.server?.map(
                                                                                        (item) => {
                                                                                            return {
                                                                                                label: item.envName,
                                                                                                value: item.envId
                                                                                            };
                                                                                        }
                                                                                    ) ?? []
                                                                                }
                                                                                allowClear
                                                                            />
                                                                        </Form.Item>
                                                                        <CardTitle text="断言配置" />
                                                                        <Form.Item
                                                                            label="JSON Schema 校验"
                                                                            tooltip="开启后，将使用开启的 JSON Schema 断言对各服务的返回结果做校验"
                                                                            name="jsonSchemaCheck"
                                                                        >
                                                                            <Switch />
                                                                        </Form.Item>
                                                                        <Form.Item
                                                                            label="智能去噪"
                                                                            name="intelligentNoiseReduce"
                                                                            tooltip="开启后，在 Diff 忽略 Key 断言失败时，会智能计算噪声 Key 并回写到断言，再次执行 Diff 忽略 Key 断言校验"
                                                                        >
                                                                            <Switch />
                                                                        </Form.Item>
                                                                    </>
                                                                ) : null}

                                                                <NotificationConfig
                                                                    showMore={serverShowMore}
                                                                    setShowMore={setServerShowMore}
                                                                />
                                                                {/* {creationMethod === 'custom' && ( */}

                                                                <></>
                                                            </>
                                                        )}
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                    </Form>
                                </div>
                            </>
                        </div>
                        <CaseNodeTree
                            key={templateId}
                            ref={caseNodeTreeModalRef}
                            caseNodeList={caseNodeList}
                            setCaseNodeList={(e) => {
                                setCaseNodeList(e);
                            }}
                        />
                        {/* <CreatTemplateModal ref={creatTemplateModalRef} /> */}
                        {/* <TemplateManager ref={templateManagerRef} /> */}
                    </Spin>
                </di>
            </Modal>
        </>
    );
});

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule,
    planList: state.common.plan.planList,
    currentPlanGroup: state.common.plan.currentPlanGroup,
    serverList: state.common.case.serverList,
    currentGroup: state.common.case.currentGroup
}))(CreateTemplateModal);
