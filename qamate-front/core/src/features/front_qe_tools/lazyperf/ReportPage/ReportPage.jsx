import { useState, useCallback, useEffect } from 'react';
import { message, Divider } from 'antd';
import { getSpeedRoundReport } from 'COMMON/api/front_qe_tools/lazyperf';
import InfoTitle from './components/InfoTitle';
import InfoFilter from './components/InfoFilter';
import ReportOutput from './components/ReportOutput';
import styles from './ReportPage.module.less';

const ReportPage = (props) => {
    const { curTask } = props;

    const [reportTitle, setReportTitle] = useState('性能评测报告');
    const [reportNames, setReportNames] = useState([]); // 报告模块名称列表
    const [reportDataArray, setReportDataArray] = useState([]); // 报告数据列表
    const [reportDescList, setReportDescList] = useState([]); // 报告描述列表
    const [reportTableDataList, setReportTableDataList] = useState([]); // 报告表格数据列表
    const [reportInfoList, setReportInfoList] = useState([]); // 报告筛选数据列表

    // 筛选选项状态
    const [reportTaskOption, setReportTaskOption] = useState({
        caseId: null,
        sceneId: null,
        modalIndex: null
    });

    // 场景列表状态
    const [sceneList, setSceneList] = useState([]);

    const [loading, setLoading] = useState(false);

    // 当用例改变时，更新场景列表（参考 FramePage 的实现）
    useEffect(() => {
        if (!reportTaskOption.caseId || !curTask) {
            setSceneList([]);
            return;
        }

        const curCaseNode = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
            (item) => item?.caseNodeId === reportTaskOption.caseId
        );
        const newSceneList = curCaseNode?.sceneList || [];
        setSceneList(newSceneList);

        // 如果当前选中的场景不在新的场景列表中，清空场景选择
        if (
            reportTaskOption.sceneId &&
            !newSceneList.find((scene) => scene.id === reportTaskOption.sceneId)
        ) {
            setReportTaskOption((prev) => ({
                ...prev,
                sceneId: null
            }));
        }
    }, [reportTaskOption.caseId, curTask, reportTaskOption.sceneId]);

    // 添加新模块。纯前端
    const addNewModule = () => {
        const newReportNames = [...reportNames, `报告 ${reportNames.length + 1}`];
        const newReportDataArray = [...reportDataArray, []];
        const newReportDescList = [...reportDescList, ''];
        const newReportTableDataList = [...reportTableDataList, []];
        const newReportInfoList = [...reportInfoList, []];

        setReportNames(newReportNames);
        setReportDataArray(newReportDataArray);
        setReportDescList(newReportDescList);
        setReportTableDataList(newReportTableDataList);
        setReportInfoList(newReportInfoList);

        message.success('新模块添加成功');
    };

    // 上移模块
    const moveModuleUp = (index) => {
        if (index === 0) {
            message.warning('已经是第一个模块');
            return;
        }

        const newReportNames = [...reportNames];
        const newReportDataArray = [...reportDataArray];
        const newReportDescList = [...reportDescList];
        const newReportTableDataList = [...reportTableDataList];
        const newReportInfoList = [...reportInfoList];

        // 交换当前模块和上一个模块的位置
        [newReportNames[index], newReportNames[index - 1]] = [
            newReportNames[index - 1],
            newReportNames[index]
        ];
        [newReportDataArray[index], newReportDataArray[index - 1]] = [
            newReportDataArray[index - 1],
            newReportDataArray[index]
        ];
        [newReportDescList[index], newReportDescList[index - 1]] = [
            newReportDescList[index - 1],
            newReportDescList[index]
        ];
        [newReportTableDataList[index], newReportTableDataList[index - 1]] = [
            newReportTableDataList[index - 1],
            newReportTableDataList[index]
        ];
        [newReportInfoList[index], newReportInfoList[index - 1]] = [
            newReportInfoList[index - 1],
            newReportInfoList[index]
        ];

        setReportNames(newReportNames);
        setReportDataArray(newReportDataArray);
        setReportDescList(newReportDescList);
        setReportTableDataList(newReportTableDataList);
        setReportInfoList(newReportInfoList);
    };

    // 下移模块
    const moveModuleDown = (index) => {
        if (index === reportNames.length - 1) {
            message.warning('已经是最后一个模块');
            return;
        }

        const newReportNames = [...reportNames];
        const newReportDataArray = [...reportDataArray];
        const newReportDescList = [...reportDescList];
        const newReportTableDataList = [...reportTableDataList];
        const newReportInfoList = [...reportInfoList];

        // 交换当前模块和下一个模块的位置
        [newReportNames[index], newReportNames[index + 1]] = [
            newReportNames[index + 1],
            newReportNames[index]
        ];
        [newReportDataArray[index], newReportDataArray[index + 1]] = [
            newReportDataArray[index + 1],
            newReportDataArray[index]
        ];
        [newReportDescList[index], newReportDescList[index + 1]] = [
            newReportDescList[index + 1],
            newReportDescList[index]
        ];
        [newReportTableDataList[index], newReportTableDataList[index + 1]] = [
            newReportTableDataList[index + 1],
            newReportTableDataList[index]
        ];
        [newReportInfoList[index], newReportInfoList[index + 1]] = [
            newReportInfoList[index + 1],
            newReportInfoList[index]
        ];

        setReportNames(newReportNames);
        setReportDataArray(newReportDataArray);
        setReportDescList(newReportDescList);
        setReportTableDataList(newReportTableDataList);
        setReportInfoList(newReportInfoList);
    };

    // 获取报告数据并添加到指定模块
    const addDataToReport = useCallback(async () => {
        const { caseId, sceneId, modalIndex } = reportTaskOption;

        if (!caseId) {
            message.warning('用例不得为空');
            return;
        }
        if (!sceneId) {
            message.warning('场景不得为空');
            return;
        }
        if (modalIndex === null || modalIndex === undefined) {
            message.warning('报告模块不得为空');
            return;
        }
        if (!curTask?.planId) {
            message.warning('任务信息不完整');
            return;
        }

        setLoading(true);
        try {
            const params = {
                planId: parseInt(curTask.planId),
                caseNodeId: parseInt(caseId),
                sceneId: parseInt(sceneId)
            };

            const res = await getSpeedRoundReport(params);

            // 更新对应模块的数据
            const newReportDataArray = [...reportDataArray];
            const newReportTableDataList = [...reportTableDataList];
            const newReportInfoList = [...reportInfoList];

            // 构造报告数据结构
            const processedData = {
                min: res?.min,
                max: res?.max,
                avg: res?.avg,
                confidence: res?.confidence,
                totalCount: res?.totalCount,
                actualCount: res?.actualCount
            };

            // 构造表格数据 - 从 curTask 中获取用例和场景信息
            const selectedCase = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
                (item) => item?.caseNodeId === caseId
            );
            const selectedScene = sceneList.find((sceneItem) => sceneItem.id === sceneId);

            const tableDataItem = {
                key: `${curTask.planId}_${caseId}_${sceneId}`,
                suiteName: curTask?.planName || '未知任务',
                caseName: selectedCase?.caseNodeName || '未知用例',
                sceneName: selectedScene?.name || '未知场景',
                avg: res?.avg,
                max: res?.max,
                min: res?.min,
                rowSpan: 1 // 表格合并行用
            };

            newReportDataArray[modalIndex] = processedData;
            newReportTableDataList[modalIndex] = [tableDataItem]; // 表格数据
            newReportInfoList[modalIndex] = [{ caseId, sceneId }];

            setReportDataArray(newReportDataArray);
            setReportTableDataList(newReportTableDataList);
            setReportInfoList(newReportInfoList);

            message.success('数据添加成功');
        } finally {
            setLoading(false);
        }
    }, [
        reportTaskOption,
        reportDataArray,
        reportTableDataList,
        reportInfoList,
        curTask,
        sceneList
    ]);

    return (
        <div className={styles.content} style={{ height: 'auto', overflow: 'visible' }}>
            <div className={styles.reportPageContent}>
                <InfoTitle
                    reportTitle={reportTitle}
                    onTitleChange={setReportTitle}
                    onAddModule={addNewModule}
                />

                {reportNames.length > 0 && (
                    <div>
                        <InfoFilter
                            curTask={curTask}
                            sceneList={sceneList}
                            reportTaskOption={reportTaskOption}
                            onOptionChange={setReportTaskOption}
                            reportNames={reportNames}
                            onAddData={addDataToReport}
                            loading={loading}
                        />
                        <Divider style={{ margin: 0, padding: 0 }} />
                    </div>
                )}

                {/* 报告内容展示 */}
                <ReportOutput
                    reportTitle={reportTitle}
                    reportNames={reportNames}
                    reportDataArray={reportDataArray}
                    reportDescList={reportDescList}
                    reportTableDataList={reportTableDataList}
                    onUpdateTitle={(index, title) => {
                        const newNames = [...reportNames];
                        newNames[index] = title;
                        setReportNames(newNames);
                    }}
                    onUpdateDescription={(index, desc) => {
                        const newDescList = [...reportDescList];
                        newDescList[index] = desc;
                        setReportDescList(newDescList);
                    }}
                    onDeleteModule={(index) => {
                        setReportNames(reportNames.filter((_, i) => i !== index));
                        setReportDataArray(reportDataArray.filter((_, i) => i !== index));
                        setReportDescList(reportDescList.filter((_, i) => i !== index));
                        setReportTableDataList(reportTableDataList.filter((_, i) => i !== index));
                        setReportInfoList(reportInfoList.filter((_, i) => i !== index));
                        message.success('模块删除成功');
                    }}
                    onMoveUp={moveModuleUp}
                    onMoveDown={moveModuleDown}
                />
            </div>
        </div>
    );
};

export default ReportPage;
